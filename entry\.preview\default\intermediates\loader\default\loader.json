{"modulePathMap": {"entry": "D:\\Code\\harmony\\Weather_Update2\\entry"}, "compileMode": "esmodule", "projectRootPath": "D:\\Code\\harmony\\Weather_Update2", "nodeModulesPath": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\loader_out\\default\\node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "entry", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": [], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "anBuildOutPut": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\loader_out\\default\\an\\arm64-v8a", "anBuildMode": "type", "buildConfigPath": ".preview\\config\\buildConfig.json"}