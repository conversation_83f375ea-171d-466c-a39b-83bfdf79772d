if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ApiTest_Params {
    testResult?: string;
    isLoading?: boolean;
}
import http from "@ohos:net.http";
import { CommonConstants } from "@normalized:N&&&entry/src/main/ets/constants/CommonConstants&";
class ApiTest extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__testResult = new ObservedPropertySimplePU('点击测试按钮开始测试API', this, "testResult");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ApiTest_Params) {
        if (params.testResult !== undefined) {
            this.testResult = params.testResult;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: ApiTest_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__testResult.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__testResult.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __testResult: ObservedPropertySimplePU<string>;
    get testResult() {
        return this.__testResult.get();
    }
    set testResult(newValue: string) {
        this.__testResult.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    async testWeatherApi() {
        this.isLoading = true;
        this.testResult = '正在测试API...';
        try {
            // 使用北京的经纬度进行测试
            const testLocation = '39.904200,116.407396'; // 北京天安门
            const url = `https://api.jisuapi.com/weather/query?appkey=${CommonConstants.APP_CODE}&city=&location=${testLocation}`;
            console.log('测试URL:', url);
            let httpRequest = http.createHttp();
            const response = await httpRequest.request(url, {
                method: http.RequestMethod.GET,
                connectTimeout: 10000,
                readTimeout: 10000,
            });
            console.log('响应状态码:', response.responseCode);
            console.log('响应数据:', response.result.toString());
            if (response.responseCode === 200) {
                const result = JSON.parse(response.result.toString());
                if (result.status === 0 && result.msg === "ok") {
                    this.testResult = `API测试成功！\n当前温度: ${result.result.temp}℃\n天气状况: ${result.result.weather}\nAQI: ${result.result.aqi.aqi}`;
                }
                else {
                    this.testResult = `API返回错误:\n状态码: ${result.status}\n错误信息: ${result.msg}`;
                }
            }
            else {
                this.testResult = `HTTP请求失败:\n状态码: ${response.responseCode}\n响应: ${response.result?.toString()}`;
            }
            httpRequest.destroy();
        }
        catch (error) {
            console.error('API测试失败:', JSON.stringify(error));
            this.testResult = `API测试失败:\n${JSON.stringify(error)}`;
        }
        this.isLoading = false;
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 20 });
            Column.debugLine("entry/src/main/ets/pages/Test/ApiTest.ets(57:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Color.White);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('天气API测试');
            Text.debugLine("entry/src/main/ets/pages/Test/ApiTest.ets(58:7)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ top: 50 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`当前API密钥: ${CommonConstants.APP_CODE}`);
            Text.debugLine("entry/src/main/ets/pages/Test/ApiTest.ets(63:7)", "entry");
            Text.fontSize(14);
            Text.fontColor(Color.Gray);
            Text.margin({ horizontal: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试API');
            Button.debugLine("entry/src/main/ets/pages/Test/ApiTest.ets(68:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.testWeatherApi();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/Test/ApiTest.ets(77:9)", "entry");
                        LoadingProgress.width(50);
                        LoadingProgress.height(50);
                    }, LoadingProgress);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.testResult);
            Text.debugLine("entry/src/main/ets/pages/Test/ApiTest.ets(82:7)", "entry");
            Text.fontSize(16);
            Text.margin({ horizontal: 20 });
            Text.textAlign(TextAlign.Start);
            Text.width('90%');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('如果API测试失败，可能的原因：\n1. API密钥已过期或无效\n2. 网络连接问题\n3. API服务暂时不可用\n4. 权限配置问题');
            Text.debugLine("entry/src/main/ets/pages/Test/ApiTest.ets(88:7)", "entry");
            Text.fontSize(12);
            Text.fontColor(Color.Gray);
            Text.margin({ horizontal: 20, top: 30 });
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "ApiTest";
    }
}
registerNamedRoute(() => new ApiTest(undefined, {}), "", { bundleName: "com.example.weather", moduleName: "entry", pagePath: "pages/Test/ApiTest", pageFullPath: "entry/src/main/ets/pages/Test/ApiTest", integratedHsp: "false", moduleType: "followWithHap" });
