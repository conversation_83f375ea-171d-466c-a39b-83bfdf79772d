import { HourlyViewModel } from '../viewmodel/HourlyViewModel';
/**
 * 未来一小时天气视图组件，包括时间、天气图标和温度组成。
 */
@Component
export struct ListHourlyItemComponent {
  @Prop itemInfo: HourlyViewModel;
  build() {
    Column() {
      Text(this.itemInfo.time)
        .fontSize($r('app.float.common_font_size'))
      Image($rawfile(this.itemInfo.img))
        .objectFit(ImageFit.Contain)
        .width($r('app.float.loction_pic_size'))
        .height($r('app.float.loction_pic_size'))
      Text(this.itemInfo.temp)
        .fontSize($r('app.float.common_font_size'))
    }.margin({
      right: $r('app.float.common_space_size'),
      left: $r('app.float.common_space_size')
    })
  }
}