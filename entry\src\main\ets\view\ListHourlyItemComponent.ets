import { HourlyViewModel } from '../viewmodel/HourlyViewModel';
/**
 * 未来一小时天气视图组件，包括时间、天气图标和温度组成。
 */
@Component
export struct ListHourlyItemComponent {
  private itemInfo: HourlyViewModel |null=null;
  build() {
    Column() {
      Text((this.itemInfo as HourlyViewModel).time)
        .fontSize($r('app.float.common_font_size'))
      Image($rawfile((this.itemInfo as HourlyViewModel).img))
        .objectFit(ImageFit.Contain)
        .width($r('app.float.loction_pic_size'))
        .height($r('app.float.loction_pic_size'))
      Text((this.itemInfo as HourlyViewModel).temp)
        .fontSize($r('app.float.common_font_size'))
    }.margin({
      right: $r('app.float.common_space_size'),
      left: $r('app.float.common_space_size')
    })
  }
}