import hilog from '@ohos.hilog';
import geolocation from '@ohos.geolocation';
import { geoLocationManager } from '@kit.LocationKit';
import { Callback } from '@kit.BasicServicesKit';
import { locationManager } from '@kit.MDMKit';
const TAG='GeoDataHelper '
/**
 *地理位置数据辅助类
 */
export class GeoDataHelper {
/**
 * 返回当前地理位置
 * @param locationChange  回调函数
 */
static async getGeoLocation(locationChange:Callback<geoLocationManager.Location>) {
  console.log(TAG + '开始获取地理位置...');

  // 首先检查位置服务是否可用
  try {
    const isEnabled = geoLocationManager.isLocationEnabled();
    console.log(TAG + '位置服务是否启用:', isEnabled);

    if (!isEnabled) {
      console.error(TAG + '位置服务未启用，请在设置中开启位置服务');
      return;
    }
  } catch (error) {
    console.error(TAG + '检查位置服务状态失败:', JSON.stringify(error));
  }

  //设置订阅位置服务状态变化的相关参数.
  const requestInfo:geoLocationManager.LocationRequest = {
    'priority': geoLocationManager.LocationRequestPriority.FIRST_FIX,
    'scenario': geoLocationManager.LocationRequestScenario.UNSET,
    'maxAccuracy': 0,
    'timeInterval': 1,
    'distanceInterval': 0
  };

  console.log(TAG + '位置请求参数:', JSON.stringify(requestInfo));

  try {
    //订阅位置服务状态变化。
    console.log(TAG + '开始订阅位置变化...');
    geoLocationManager.on("locationChange", requestInfo, locationChange);
    console.log(TAG + '位置订阅成功');
  } catch (err) {
    console.error(TAG + '位置订阅失败:', JSON.stringify(err));
    hilog.error(0x0000, 'GeoDataHelper', '位置订阅失败: %{public}s', JSON.stringify(err));
  }
}

/**
 * 获取单次位置
 */
static async getCurrentLocation(): Promise<geoLocationManager.Location | null> {
  console.log(TAG + '尝试获取单次位置...');

  const requestInfo:geoLocationManager.CurrentLocationRequest = {
    'priority': geoLocationManager.LocationRequestPriority.FIRST_FIX,
    'scenario': geoLocationManager.LocationRequestScenario.UNSET,
    'maxAccuracy': 0,
    'timeoutMs': 10000
  };

  try {
    const location = await geoLocationManager.getCurrentLocation(requestInfo);
    console.log(TAG + '单次位置获取成功:', JSON.stringify(location));
    return location;
  } catch (error) {
    console.error(TAG + '单次位置获取失败:', JSON.stringify(error));
    return null;
  }
}
/**
 * 返回地理描述位置
 * @param location 地理坐标位置
 */
static async getAddr(location:geoLocationManager.Location){
  console.log(TAG+'正在获取城市位置信息----')
  let geoAddress = new Array<geoLocationManager.GeoAddress>()
  await geoLocationManager.getAddressesFromLocation(location).then((data) => {
      geoAddress=data;
    });
  console.log(TAG+'城市位置信息为----'+JSON.stringify(geoAddress))
  return geoAddress
}
/**
 * 根据地理位置对象，返回字符串形式的经纬度
 * @param location  地理位置
 */
static getLatLon(location:geoLocationManager.Location) {
  let latitude =  location.latitude;
  let longitude = location.longitude;
  let addressLL = Number(latitude).toFixed(6) + ','
  + Number(longitude).toFixed(6);
  return addressLL
  }
}