import {checkPermissions} from './PermissionGrant'
import { geoLocationManager } from '@kit.LocationKit';
import { AlertDialog, promptAction } from '@kit.ArkUI';

const TAG='LoacationKitTestPage '
@Entry
@Component
struct LocationKitTestPage {
  @State message:string='hello'
  @State location: geoLocationManager.Location|null=null
  build() {
    Column({space:20}) {
      Button('用户申请位置授权')
        .onClick(async ()=>{
          const promiseString: Promise<string> = checkPermissions()
          checkPermissions()
          promiseString.then((stringValue: string) => {
            this.message=stringValue
          });
        })

      Button('判断位置开关是否打开')
        .onClick( ()=>{
          try {
            let locationEnabled = geoLocationManager.isLocationEnabled();
            if(locationEnabled==true) promptAction.showToast({message:'开关已打开'})
            else promptAction.showToast({message:'开关未打开'})
          } catch (err) {
            promptAction.showToast({message:"errCode:" + err.code + ", message:"  + err.message})
          }
        })

      Button('获取当前的实时位置')
        .onClick( ()=>{
          try {
              geoLocationManager.on('locationChange',{},(location)=>{
              this.location=location
            });
          } catch (err) {
            promptAction.showToast({message:"errCode:" + err.code + ", message:"  + err.message})
          }
        })

      Button('判断服务是否可用')
        .onClick( ()=>{
          const isAvailable= geoLocationManager.isGeocoderAvailable()
          promptAction.showToast({message:'服务是否支持'+isAvailable})
        })

      Button('地理解析')
        .onClick(async  ()=>{
          const location=await geoLocationManager.getAddressesFromLocationName({
            description:'广州市'
          })
          promptAction.showToast({message:JSON.stringify(location,null,2)})
        })

      Text(JSON.stringify(this.location,null,2))

    }
    .height('100%')
    .width('100%')
  }
}