if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface MainPage_Params {
    city?: string;
    aqiViewModel?: AqiViewModel;
    nowViewModel?: NowViewModel;
    hourlyViewModel?: Array<HourlyViewModel>;
    dailyViewModel?: Array<DailyViewModel>;
}
import { GeoDataHelper } from "@normalized:N&&&entry/src/main/ets/dal/GeoDataHelper&";
import type { DailyViewModel } from '../viewmodel/DailyViewModel';
import { ListDailyAreaComponent } from "@normalized:N&&&entry/src/main/ets/view/ListDailyAreaComponent&";
import { CommonConstants } from "@normalized:N&&&entry/src/main/ets/constants/CommonConstants&";
import { ListHourlyAreaComponent } from "@normalized:N&&&entry/src/main/ets/view/ListHourlyAreaComponent&";
import { AddressAreaComponent } from "@normalized:N&&&entry/src/main/ets/view/AddressAreaComponent&";
import { WeatherDataHelper } from "@normalized:N&&&entry/src/main/ets/dal/WeatherDataHelper&";
import { HttpGet } from "@normalized:N&&&entry/src/main/ets/util/HttpGet&";
import { NowAreaComponent } from "@normalized:N&&&entry/src/main/ets/view/NowAreaComponent&";
import { AqiAreaComponent } from "@normalized:N&&&entry/src/main/ets/view/AqiAreaComponent&";
import { AqiViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/AqiViewModel&";
import { NowViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/NowViewModel&";
import type { HourlyViewModel } from '../viewmodel/HourlyViewModel';
import geoLocationManager from "@ohos:geoLocationManager";
import { checkPermissions } from "@normalized:N&&&entry/src/main/ets/util/PermissionGrant&";
/**
 * 主界面，由5个子视图组件组成：位置、当前天气、AQI信息、未来12小时预报和未来七天预报。
 */
const TAG = 'MainPage ';
class MainPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__city = new ObservedPropertySimplePU(''
        //Aqi组件数据模型
        , this, "city");
        this.__aqiViewModel = new ObservedPropertyObjectPU(new AqiViewModel()
        //当前天气组件数据模型
        , this, "aqiViewModel");
        this.__nowViewModel = new ObservedPropertyObjectPU(new NowViewModel()
        //未来24小时组件数据模型
        , this, "nowViewModel");
        this.__hourlyViewModel = new ObservedPropertyObjectPU(new Array<HourlyViewModel>(), this, "hourlyViewModel");
        this.__dailyViewModel = new ObservedPropertyObjectPU(new Array<DailyViewModel>(), this, "dailyViewModel");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: MainPage_Params) {
        if (params.city !== undefined) {
            this.city = params.city;
        }
        if (params.aqiViewModel !== undefined) {
            this.aqiViewModel = params.aqiViewModel;
        }
        if (params.nowViewModel !== undefined) {
            this.nowViewModel = params.nowViewModel;
        }
        if (params.hourlyViewModel !== undefined) {
            this.hourlyViewModel = params.hourlyViewModel;
        }
        if (params.dailyViewModel !== undefined) {
            this.dailyViewModel = params.dailyViewModel;
        }
    }
    updateStateVars(params: MainPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__city.purgeDependencyOnElmtId(rmElmtId);
        this.__aqiViewModel.purgeDependencyOnElmtId(rmElmtId);
        this.__nowViewModel.purgeDependencyOnElmtId(rmElmtId);
        this.__hourlyViewModel.purgeDependencyOnElmtId(rmElmtId);
        this.__dailyViewModel.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__city.aboutToBeDeleted();
        this.__aqiViewModel.aboutToBeDeleted();
        this.__nowViewModel.aboutToBeDeleted();
        this.__hourlyViewModel.aboutToBeDeleted();
        this.__dailyViewModel.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    //城市名称
    private __city: ObservedPropertySimplePU<string>;
    get city() {
        return this.__city.get();
    }
    set city(newValue: string) {
        this.__city.set(newValue);
    }
    //Aqi组件数据模型
    private __aqiViewModel: ObservedPropertyObjectPU<AqiViewModel>;
    get aqiViewModel() {
        return this.__aqiViewModel.get();
    }
    set aqiViewModel(newValue: AqiViewModel) {
        this.__aqiViewModel.set(newValue);
    }
    //当前天气组件数据模型
    private __nowViewModel: ObservedPropertyObjectPU<NowViewModel>;
    get nowViewModel() {
        return this.__nowViewModel.get();
    }
    set nowViewModel(newValue: NowViewModel) {
        this.__nowViewModel.set(newValue);
    }
    //未来24小时组件数据模型
    private __hourlyViewModel: ObservedPropertyObjectPU<Array<HourlyViewModel>>;
    get hourlyViewModel() {
        return this.__hourlyViewModel.get();
    }
    set hourlyViewModel(newValue: Array<HourlyViewModel>) {
        this.__hourlyViewModel.set(newValue);
    }
    //未来7天组件数据模型
    private __dailyViewModel: ObservedPropertyObjectPU<Array<DailyViewModel>>;
    get dailyViewModel() {
        return this.__dailyViewModel.get();
    }
    set dailyViewModel(newValue: Array<DailyViewModel>) {
        this.__dailyViewModel.set(newValue);
    }
    /**
     * 请求应用对应的权限
     */
    async requestPermissions() {
        //stage版本
        //请求获取权限
        await checkPermissions().then((res: string) => {
            console.log(TAG + 'checkPermissions()返回的结果为：' + res);
        })
            .catch((err: string) => {
            console.error(TAG + 'checkPermissions()报错结果为：' + err);
        });
    }
    // 处理位置信息的方法
    async processLocation(location: geoLocationManager.Location) {
        try {
            let geoAddr = await GeoDataHelper.getAddr(location);
            //用于显示城市名称
            console.log(TAG + '城市信息:' + JSON.stringify(geoAddr));
            if (geoAddr && geoAddr.length > 0) {
                this.city = geoAddr[0].locality + (geoAddr[0].subLocality as string || '');
                console.log(TAG + '城市名称:' + JSON.stringify(this.city));
            }
            else {
                this.city = "未知位置";
                console.log(TAG + '无法获取城市名称，使用默认值');
            }
            //定义http请求工具
            let httpGet = new HttpGet();
            //获得字符串格式的经纬度
            let addressLL = GeoDataHelper.getLatLon(location);
            console.log(TAG + '城市经纬度:' + JSON.stringify(addressLL));
            //得到位置后，向天气预报接口发起网络请求
            const model = await httpGet.doGet(addressLL);
            console.log(TAG + "获取到天气数据模型:", JSON.stringify(model));
            // 检查模型数据是否有效
            if (model && model.result && model.status === 0) {
                console.log(TAG + "开始处理天气数据...");
                // 处理AQI数据
                if (model.result.aqi) {
                    this.aqiViewModel = WeatherDataHelper.getAqiViewModel(model);
                    console.log(TAG + "AQI数据处理完成:", JSON.stringify(this.aqiViewModel));
                }
                // 处理当前天气数据
                if (model.result.temp && model.result.weather) {
                    this.nowViewModel = WeatherDataHelper.getNowViewModel(model);
                    console.log(TAG + "当前天气数据处理完成:", JSON.stringify(this.nowViewModel));
                }
                // 处理小时预报数据
                if (model.result.hourly && model.result.hourly.length > 0) {
                    this.hourlyViewModel = WeatherDataHelper.getHourListDataSource(model);
                    console.log(TAG + "小时预报数据处理完成，数量:", this.hourlyViewModel.length);
                }
                // 处理日预报数据
                if (model.result.daily && model.result.daily.length > 0) {
                    this.dailyViewModel = WeatherDataHelper.getDailyListDataSource(model);
                    console.log(TAG + "日预报数据处理完成，数量:", this.dailyViewModel.length);
                }
                console.log(TAG + "所有天气数据处理完成");
            }
            else {
                console.error(TAG + "天气数据无效或API返回错误");
                console.error(TAG + "模型状态:", model?.status, "消息:", model?.msg);
            }
        }
        catch (error) {
            console.error(TAG + "处理位置信息时发生错误:", JSON.stringify(error));
        }
    }
    async aboutToAppear() {
        await this.requestPermissions();
        console.log(TAG + '权限申请完成');
        // 先尝试获取最后一次的位置
        try {
            let location = await geoLocationManager.getLastLocation();
            console.info(TAG + '获取到最后位置: ' + JSON.stringify(location));
            if (location) {
                console.log(TAG + '使用最后位置信息');
                // 如果有最后位置，直接使用
                await this.processLocation(location);
            }
            else {
                console.log(TAG + '没有最后位置信息，开始实时定位');
            }
        }
        catch (error) {
            console.error(TAG + '获取最后位置失败:', JSON.stringify(error));
        }
        const locationChange = (async (location: geoLocationManager.Location) => {
            console.log(TAG + "收到位置更新:", JSON.stringify(location));
            await this.processLocation(location);
        });
        //请求位置数据
        GeoDataHelper.getGeoLocation(locationChange);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: CommonConstants.AREA_SPACE_SIZE });
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(148:3)", "entry");
            Column.height("100%");
            Column.alignItems(HorizontalAlign.Start);
            Column.backgroundImage({ "id": 16777227, "type": 20000, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Column.backgroundImageSize(ImageSize.Cover);
            Column.backgroundImagePosition(Alignment.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(150:5)", "entry");
            Row.height("5%");
        }, Row);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new AddressAreaComponent(this, { city: this.__city }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MainPage.ets", line: 151, col: 7 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            city: this.city
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "AddressAreaComponent" });
        }
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(154:5)", "entry");
            Row.height("15%");
        }, Row);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new NowAreaComponent(this, { nowViewModel: this.__nowViewModel }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MainPage.ets", line: 155, col: 7 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            nowViewModel: this.nowViewModel
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "NowAreaComponent" });
        }
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(158:5)", "entry");
            Row.height("15%");
        }, Row);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new AqiAreaComponent(this, { aqiViewModel: this.__aqiViewModel }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MainPage.ets", line: 159, col: 7 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            aqiViewModel: this.aqiViewModel
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "AqiAreaComponent" });
        }
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(162:5)", "entry");
            Row.height("12%");
        }, Row);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new ListHourlyAreaComponent(this, { hourlyViewModel: this.__hourlyViewModel }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MainPage.ets", line: 163, col: 7 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            hourlyViewModel: this.hourlyViewModel
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "ListHourlyAreaComponent" });
        }
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(166:5)", "entry");
        }, Row);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new ListDailyAreaComponent(this, { dailyViewModel: this.__dailyViewModel }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MainPage.ets", line: 167, col: 7 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            dailyViewModel: this.dailyViewModel
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "ListDailyAreaComponent" });
        }
        Row.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "MainPage";
    }
}
registerNamedRoute(() => new MainPage(undefined, {}), "", { bundleName: "com.example.weather", moduleName: "entry", pagePath: "pages/MainPage", pageFullPath: "entry/src/main/ets/pages/MainPage", integratedHsp: "false", moduleType: "followWithHap" });
