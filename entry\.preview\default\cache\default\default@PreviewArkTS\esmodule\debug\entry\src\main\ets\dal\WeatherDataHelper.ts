import { DailyViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/DailyViewModel&";
import { HourlyViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/HourlyViewModel&";
import { NowViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/NowViewModel&";
import type { WeatherModel } from '../bean/WeatherModel';
import { CommonConstants } from "@normalized:N&&&entry/src/main/ets/constants/CommonConstants&";
import { AqiViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/AqiViewModel&";
import { TimeFormat } from "@normalized:N&&&entry/src/main/ets/util/TimeFormat&";
/**
 *天气视图数据模型生成辅助类，用来根据天气数据生成对应的视图数据模型
 */
export class WeatherDataHelper {
    /**
     * 生成AQI区域的数据模型
     * @param model 天气数据对象
     */
    static getAqiViewModel(model: WeatherModel) {
        let viewmodel = new AqiViewModel();
        viewmodel.aqi = Number.parseInt(model.result.aqi.aqi);
        viewmodel.quality = model.result.aqi.quality;
        return viewmodel;
    }
    /**
     * 生成当前天气区域的数据模型
     * @param model 天气数据对象
     */
    static getNowViewModel(model: WeatherModel) {
        let viewmodel = new NowViewModel();
        viewmodel.weather = model.result.weather;
        viewmodel.temp = model.result.temp + "℃";
        viewmodel.templow = "最低" + model.result.templow + "℃";
        viewmodel.temphigh = "最高" + model.result.temphigh + "℃";
        return viewmodel;
    }
    /**
     * 生成未来12小时天气区域的数据模型
     * @param model 天气数据对象
     */
    static getHourListDataSource(model: WeatherModel): Array<HourlyViewModel> {
        let listItems: Array<HourlyViewModel> = [];
        for (let i = 0; i < CommonConstants.HOURLY_LIST_SIZE; i++) {
            let itemInfo: HourlyViewModel = new HourlyViewModel();
            itemInfo.temp = model.result.hourly[i].temp + "℃";
            itemInfo.img = "weathercn/w" + model.result.hourly[i].img + ".png";
            let hour = model.result.hourly[i].time.split(':')[0];
            itemInfo.time = TimeFormat.formatAMPM(Number(hour));
            listItems.push(itemInfo);
        }
        return listItems;
    }
    /**
     * 生成未来七天天气区域的数据模型
     * @param model 天气数据对象
     */
    static getDailyListDataSource(model: WeatherModel): Array<DailyViewModel> {
        let listItems: Array<DailyViewModel> = [];
        for (let i = 0; i < CommonConstants.DAILY_LIST_SIZE; i++) {
            let itemInfo: DailyViewModel = new DailyViewModel();
            itemInfo.week = model.result.daily[i].week;
            itemInfo.img = "weathercn/w" + model.result.daily[i].day.img + ".png";
            itemInfo.windpower = model.result.daily[i].day.windpower;
            itemInfo.templow = model.result.daily[i].night.templow + "℃";
            itemInfo.temphigh = model.result.daily[i].day.temphigh + "℃";
            listItems.push(itemInfo);
        }
        return listItems;
    }
}
let weatherDataHelper = new WeatherDataHelper();
export default weatherDataHelper as WeatherDataHelper;
