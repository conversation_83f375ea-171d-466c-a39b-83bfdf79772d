if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ListHourlyAreaComponent_Params {
    hourlyViewModel?: HourlyViewModel[];
}
import type { HourlyViewModel } from '../viewmodel/HourlyViewModel';
import { ListHourlyItemComponent } from "@normalized:N&&&entry/src/main/ets/view/ListHourlyItemComponent&";
export class ListHourlyAreaComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__hourlyViewModel = new SynchedPropertyObjectTwoWayPU(params.hourlyViewModel, this, "hourlyViewModel");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ListHourlyAreaComponent_Params) {
    }
    updateStateVars(params: ListHourlyAreaComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__hourlyViewModel.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__hourlyViewModel.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __hourlyViewModel: SynchedPropertySimpleOneWayPU<HourlyViewModel[]>;
    get hourlyViewModel() {
        return this.__hourlyViewModel.get();
    }
    set hourlyViewModel(newValue: HourlyViewModel[]) {
        this.__hourlyViewModel.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/ListHourlyAreaComponent.ets(10:5)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            List.create();
            List.debugLine("entry/src/main/ets/view/ListHourlyAreaComponent.ets(11:7)", "entry");
            List.listDirection(Axis.Horizontal);
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const item = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                        ListItem.debugLine("entry/src/main/ets/view/ListHourlyAreaComponent.ets(13:11)", "entry");
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new ListHourlyItemComponent(this, { itemInfo: item }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/view/ListHourlyAreaComponent.ets", line: 14, col: 13 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {
                                            itemInfo: item
                                        };
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                        itemInfo: item
                                    });
                                }
                            }, { name: "ListHourlyItemComponent" });
                        }
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.hourlyViewModel, forEachItemGenFunction, (item: HourlyViewModel) => JSON.stringify(item), false, false);
        }, ForEach);
        ForEach.pop();
        List.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
