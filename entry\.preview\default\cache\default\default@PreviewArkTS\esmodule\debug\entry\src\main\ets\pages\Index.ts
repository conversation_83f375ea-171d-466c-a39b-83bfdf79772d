if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    message?: string;
}
import router from "@ohos:router";
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__message = new ObservedPropertySimplePU('天气应用导航', this, "message");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.message !== undefined) {
            this.message = params.message;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__message.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__message.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __message: ObservedPropertySimplePU<string>;
    get message() {
        return this.__message.get();
    }
    set message(newValue: string) {
        this.__message.set(newValue);
    }
    navigateToPage(url: string) {
        router.pushUrl({
            url: url
        }, router.RouterMode.Standard, (err) => {
            if (err) {
                console.log(`路由跳转失败，code is ${err.code}, message is ${err.message}`);
                return;
            }
            console.log('路由跳转成功');
        });
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 20 });
            Column.debugLine("entry/src/main/ets/pages/Index.ets(21:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.message);
            Text.debugLine("entry/src/main/ets/pages/Index.ets(22:7)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ top: 50, bottom: 30 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 主应用按钮
            Button.createWithLabel('天气应用主页');
            Button.debugLine("entry/src/main/ets/pages/Index.ets(28:7)", "entry");
            // 主应用按钮
            Button.width('80%');
            // 主应用按钮
            Button.height(50);
            // 主应用按钮
            Button.fontSize(18);
            // 主应用按钮
            Button.backgroundColor('#007DFF');
            // 主应用按钮
            Button.onClick(() => {
                this.navigateToPage('pages/MainPage');
            });
        }, Button);
        // 主应用按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // API测试按钮
            Button.createWithLabel('API连接测试');
            Button.debugLine("entry/src/main/ets/pages/Index.ets(38:7)", "entry");
            // API测试按钮
            Button.width('80%');
            // API测试按钮
            Button.height(50);
            // API测试按钮
            Button.fontSize(18);
            // API测试按钮
            Button.backgroundColor('#FF6B35');
            // API测试按钮
            Button.onClick(() => {
                this.navigateToPage('pages/Test/ApiTest');
            });
        }, Button);
        // API测试按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 调试页面按钮
            Button.createWithLabel('UI组件调试');
            Button.debugLine("entry/src/main/ets/pages/Index.ets(48:7)", "entry");
            // 调试页面按钮
            Button.width('80%');
            // 调试页面按钮
            Button.height(50);
            // 调试页面按钮
            Button.fontSize(18);
            // 调试页面按钮
            Button.backgroundColor('#4CAF50');
            // 调试页面按钮
            Button.onClick(() => {
                this.navigateToPage('pages/Test/DebugPage');
            });
        }, Button);
        // 调试页面按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 位置测试按钮
            Button.createWithLabel('位置服务测试');
            Button.debugLine("entry/src/main/ets/pages/Index.ets(58:7)", "entry");
            // 位置测试按钮
            Button.width('80%');
            // 位置测试按钮
            Button.height(50);
            // 位置测试按钮
            Button.fontSize(18);
            // 位置测试按钮
            Button.backgroundColor('#9C27B0');
            // 位置测试按钮
            Button.onClick(() => {
                this.navigateToPage('pages/Test/LocationKitTestPage');
            });
        }, Button);
        // 位置测试按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 说明文字
            Text.create('选择要测试的功能：\n\n• 天气应用主页：完整的天气应用\n• API连接测试：测试天气API是否正常\n• UI组件调试：使用模拟数据测试界面\n• 位置服务测试：测试位置权限和定位');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(68:7)", "entry");
            // 说明文字
            Text.fontSize(14);
            // 说明文字
            Text.fontColor(Color.Gray);
            // 说明文字
            Text.margin({ top: 30, left: 20, right: 20 });
            // 说明文字
            Text.textAlign(TextAlign.Start);
        }, Text);
        // 说明文字
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.example.weather", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
