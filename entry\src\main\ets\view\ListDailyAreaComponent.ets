import { ListDailyItemComponent } from './ListDailyItemComponent';
import { DailyViewModel } from '../viewmodel/DailyViewModel';
/**
 * 未来七天天气视图组件，包括两部分。
 * 一部分是列头信息，一部分是天气信息。
 */
@Component
export struct ListDailyAreaComponent {
  @Link dailyViewModel: Array<DailyViewModel>
  build() {
    Row() {
      Column() {
        Row() {
          Text("日期")
            .fontSize($r('app.float.common_font_size'))
            .layoutWeight(1)
            .fontWeight(FontWeight.Bolder)
          Text("天气")
            .fontSize($r('app.float.common_font_size'))
            .layoutWeight(1)
            .fontWeight(FontWeight.Bolder)
          Text("风力")
            .fontSize($r('app.float.common_font_size'))
            .layoutWeight(1)
            .fontWeight(FontWeight.Bolder)
          Text("最高")
            .fontSize($r('app.float.common_font_size'))
            .layoutWeight(1)
            .fontWeight(FontWeight.Bolder)
          Text("最低")
            .fontSize($r('app.float.common_font_size'))
            .layoutWeight(1)
            .fontWeight(FontWeight.Bolder)
        }
        .margin({
          bottom: $r('app.float.common_space_size'),
          left: $r('app.float.common_space_size')
        })
        Row() {
          List() {
            ForEach(this.dailyViewModel, (item: DailyViewModel) => {
              ListItem() {
                ListDailyItemComponent({ itemInfo: item })
              }
            }, (item:DailyViewModel) => JSON.stringify(item))
          }
        }
      }
    }
  }
}