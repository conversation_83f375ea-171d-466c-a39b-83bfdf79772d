import { DailyViewModel } from '../viewmodel/DailyViewModel';
/**
 * 一天天气视图组件，包括星期*、天气图标、风力、最高温度和最低温度组成。
 */
@Component
export struct ListDailyItemComponent {
  private itemInfo: DailyViewModel=new DailyViewModel();
  build() {
    Row() {
      Text(this.itemInfo.week)
        .fontSize($r('app.float.common_font_size'))
        .layoutWeight(1)
      Image($rawfile(this.itemInfo.img))
        .objectFit(ImageFit.Contain)
        .width($r('app.float.loction_pic_size'))
        .height($r('app.float.loction_pic_size'))
        .layoutWeight(1)
      Text(this.itemInfo.windpower)
        .fontSize($r('app.float.common_font_size'))
        .layoutWeight(1)
      Text(this.itemInfo.temphigh)
        .fontSize($r('app.float.common_font_size'))
        .layoutWeight(1)
      Text(this.itemInfo.templow)
        .fontSize($r('app.float.common_font_size'))
        .layoutWeight(1)
    }.width("100%")
    .margin({
      bottom: $r('app.float.common_space_size'),
      left: $r('app.float.common_space_size')
    })
  }
}