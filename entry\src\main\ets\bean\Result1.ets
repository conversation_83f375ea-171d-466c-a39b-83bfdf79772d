import { Index } from './Index'
import { Aqi } from './Aqi'
import { Daily } from './Daily'
import { Hourly } from './Hourly'
/**
 * 每个字段的解释参考 https://www.jisuapi.com/api/weather
 */
export class Result1 {
  public city: string = '';
  public cityid: number = 0;
  public citycode: string = '';
  public date: Date |null=null;
  public week: string = '';
  public temp: string = '';
  public temphigh: string = '';
  public templow: string = '';
  public humidity: string = '';
  public pressure: string = '';
  public windspeed: string = '';
  public winddirect: string = '';
  public windpower: string = '';
  public updatetime: Date|null =null;
  public index: Array<Index>=[];
  public aqi: Aqi=new Aqi();
  public daily: Array<Daily>=[];
  public hourly: Array<Hourly>=[];
  public weather:string='';
  public img:string='';
}