import { http } from '@kit.NetworkKit';
import { CommonConstants } from '../../constants/CommonConstants';

/**
 * API测试页面，用于验证天气API是否正常工作
 */
@Entry
@Component
struct ApiTest {
  @State testResult: string = '点击测试按钮开始测试API';
  @State isLoading: boolean = false;

  async testWeatherApi() {
    this.isLoading = true;
    this.testResult = '正在测试API...';

    try {
      // 使用北京的经纬度进行测试
      const testLocation = '39.904200,116.407396'; // 北京天安门
      const url = `https://api.jisuapi.com/weather/query?appkey=${CommonConstants.APP_CODE}&city=&location=${testLocation}`;
      
      console.log('测试URL:', url);
      
      let httpRequest = http.createHttp();
      
      const response = await httpRequest.request(url, {
        method: http.RequestMethod.GET,
        connectTimeout: 10000,
        readTimeout: 10000,
      });

      console.log('响应状态码:', response.responseCode);
      console.log('响应数据:', response.result.toString());

      if (response.responseCode === 200) {
        const result = JSON.parse(response.result.toString());
        
        if (result.status === 0 && result.msg === "ok") {
          this.testResult = `API测试成功！\n当前温度: ${result.result.temp}℃\n天气状况: ${result.result.weather}\nAQI: ${result.result.aqi.aqi}`;
        } else {
          this.testResult = `API返回错误:\n状态码: ${result.status}\n错误信息: ${result.msg}`;
        }
      } else {
        this.testResult = `HTTP请求失败:\n状态码: ${response.responseCode}\n响应: ${response.result?.toString()}`;
      }

      httpRequest.destroy();
    } catch (error) {
      console.error('API测试失败:', JSON.stringify(error));
      this.testResult = `API测试失败:\n${JSON.stringify(error)}`;
    }

    this.isLoading = false;
  }

  build() {
    Column({ space: 20 }) {
      Text('天气API测试')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 })

      Text(`当前API密钥: ${CommonConstants.APP_CODE}`)
        .fontSize(14)
        .fontColor(Color.Gray)
        .margin({ horizontal: 20 })

      Button('测试API')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testWeatherApi();
        })

      if (this.isLoading) {
        LoadingProgress()
          .width(50)
          .height(50)
      }

      Text(this.testResult)
        .fontSize(16)
        .margin({ horizontal: 20 })
        .textAlign(TextAlign.Start)
        .width('90%')

      Text('如果API测试失败，可能的原因：\n1. API密钥已过期或无效\n2. 网络连接问题\n3. API服务暂时不可用\n4. 权限配置问题')
        .fontSize(12)
        .fontColor(Color.Gray)
        .margin({ horizontal: 20, top: 30 })
        .textAlign(TextAlign.Start)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.White)
  }
}
