if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AddressAreaComponent_Params {
    city?: string;
}
export class AddressAreaComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__city = new SynchedPropertySimpleTwoWayPU(params.city, this, "city");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AddressAreaComponent_Params) {
    }
    updateStateVars(params: AddressAreaComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__city.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__city.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __city: SynchedPropertySimpleTwoWayPU<string>;
    get city() {
        return this.__city.get();
    }
    set city(newValue: string) {
        this.__city.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/AddressAreaComponent.ets(8:5)", "entry");
            Column.margin({
                top: { "id": 16777232, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/AddressAreaComponent.ets(9:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777226, "type": 20000, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/view/AddressAreaComponent.ets(10:9)", "entry");
            Image.height({ "id": 16777233, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Image.width({ "id": 16777233, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.city);
            Text.debugLine("entry/src/main/ets/view/AddressAreaComponent.ets(13:9)", "entry");
            Text.fontSize({ "id": 16777235, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
