import { GeoDataHelper } from '../dal/GeoDataHelper';
import { DailyViewModel } from '../viewmodel/DailyViewModel';
import { ListDailyAreaComponent } from '../view/ListDailyAreaComponent';
import { CommonConstants } from '../constants/CommonConstants';
import { ListHourlyAreaComponent } from '../view/ListHourlyAreaComponent';
import { AddressAreaComponent } from '../view/AddressAreaComponent'
import WeatherDataHelper from '../dal/WeatherDataHelper';
import hilog from '@ohos.hilog';
import { HttpGet } from '../util/HttpGet'
import { NowAreaComponent } from '../view/NowAreaComponent'
import { AqiAreaComponent } from '../view/AqiAreaComponent'
import { AqiViewModel } from '../viewmodel/AqiViewModel'
import { NowViewModel } from '../viewmodel/NowViewModel'
import { HourlyViewModel } from '../viewmodel/HourlyViewModel'
import { geoLocationManager } from '@kit.LocationKit';
import {checkPermissions} from '../util/PermissionGrant'
import {WeatherModel} from '../bean/WeatherModel'
import { promptAction } from '@kit.ArkUI';
import { BusinessError } from '@kit.BasicServicesKit';

/**
 * 主界面，由5个子视图组件组成：位置、当前天气、AQI信息、未来12小时预报和未来七天预报。
 */

const TAG='MainPage '
@Entry
@Component
struct MainPage {
//城市名称
@State city: string = ''
//Aqi组件数据模型
@State aqiViewModel: AqiViewModel = new AqiViewModel()
//当前天气组件数据模型
@State nowViewModel: NowViewModel = new NowViewModel()
//未来24小时组件数据模型
@State hourlyViewModel: Array<HourlyViewModel> =
  new Array<HourlyViewModel>();
//未来7天组件数据模型
@State dailyViewModel: Array<DailyViewModel> =
  new Array<DailyViewModel>();
  /**
   * 请求应用对应的权限
   */
   async  requestPermissions() {
    //stage版本
    //请求获取权限
    await checkPermissions().then((res:string)=>{
      console.log(TAG+'checkPermissions()返回的结果为：'+res)
    })
      .catch((err:string)=>{
        console.error(TAG+'checkPermissions()报错结果为：'+err)
      })
  }

  // 处理位置信息的方法
  async processLocation(location: geoLocationManager.Location) {
    try {
      let geoAddr = await GeoDataHelper.getAddr(location)
      //用于显示城市名称
      console.log(TAG+'城市信息:'+JSON.stringify(geoAddr))

      if (geoAddr && geoAddr.length > 0) {
        this.city = geoAddr[0].locality + (geoAddr[0].subLocality as string || '')
        console.log(TAG+'城市名称:'+JSON.stringify(this.city))
      } else {
        this.city = "未知位置"
        console.log(TAG+'无法获取城市名称，使用默认值')
      }

      //定义http请求工具
      let httpGet = new HttpGet()
      //获得字符串格式的经纬度
      let addressLL = GeoDataHelper.getLatLon(location)
      console.log(TAG+'城市经纬度:'+JSON.stringify(addressLL))

      //得到位置后，向天气预报接口发起网络请求
      const model = await httpGet.doGet(addressLL);
      console.log(TAG + "获取到天气数据模型:", JSON.stringify(model));

      // 检查模型数据是否有效
      if (model && model.result && model.status === 0) {
        console.log(TAG + "开始处理天气数据...");

        // 处理AQI数据
        if (model.result.aqi) {
          this.aqiViewModel = WeatherDataHelper.getAqiViewModel(model);
          console.log(TAG + "AQI数据处理完成:", JSON.stringify(this.aqiViewModel));
        }

        // 处理当前天气数据
        if (model.result.temp && model.result.weather) {
          this.nowViewModel = WeatherDataHelper.getNowViewModel(model);
          console.log(TAG + "当前天气数据处理完成:", JSON.stringify(this.nowViewModel));
        }

        // 处理小时预报数据
        if (model.result.hourly && model.result.hourly.length > 0) {
          this.hourlyViewModel = WeatherDataHelper.getHourListDataSource(model);
          console.log(TAG + "小时预报数据处理完成，数量:", this.hourlyViewModel.length);
        }

        // 处理日预报数据
        if (model.result.daily && model.result.daily.length > 0) {
          this.dailyViewModel = WeatherDataHelper.getDailyListDataSource(model);
          console.log(TAG + "日预报数据处理完成，数量:", this.dailyViewModel.length);
        }

        console.log(TAG + "所有天气数据处理完成");
      } else {
        console.error(TAG + "天气数据无效或API返回错误");
        console.error(TAG + "模型状态:", model?.status, "消息:", model?.msg);
      }
    } catch (error) {
      console.error(TAG + "处理位置信息时发生错误:", JSON.stringify(error));
    }
  }

async aboutToAppear() {
   await this.requestPermissions();
   console.log(TAG+'权限申请完成')

   // 先尝试获取最后一次的位置
   try {
     let location = await geoLocationManager.getLastLocation()
     console.info(TAG+'获取到最后位置: ' + JSON.stringify(location));

     if (location) {
       console.log(TAG+'使用最后位置信息')
       // 如果有最后位置，直接使用
       await this.processLocation(location);
     } else {
       console.log(TAG+'没有最后位置信息，开始实时定位')
     }
   } catch (error) {
     console.error(TAG+'获取最后位置失败:', JSON.stringify(error))
   }

  const locationChange = (async (location:geoLocationManager.Location)  => {
    console.log(TAG + "收到位置更新:", JSON.stringify(location));
    await this.processLocation(location);
  })

  //请求位置数据
  GeoDataHelper.getGeoLocation(locationChange)
}

build() {
  Column({ space: CommonConstants.AREA_SPACE_SIZE }) {

    Row() {
      AddressAreaComponent({ city: $city })
    }.height("5%")

    Row() {
      NowAreaComponent({ nowViewModel: $nowViewModel })
    }.height("15%")

    Row() {
      AqiAreaComponent({ aqiViewModel: $aqiViewModel })
    }.height("15%")

    Row() {
      ListHourlyAreaComponent({ hourlyViewModel: $hourlyViewModel })
    }.height("12%")

    Row() {
      ListDailyAreaComponent({ dailyViewModel: $dailyViewModel })
    }
  }
  .height("100%")
  .alignItems(HorizontalAlign.Start)
  .backgroundImage($r('app.media.bg4'))
  .backgroundImageSize(ImageSize.Cover)
  .backgroundImagePosition(Alignment.Center)
  }
}
