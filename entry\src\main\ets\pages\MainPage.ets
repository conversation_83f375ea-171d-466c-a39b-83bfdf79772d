import { GeoDataHelper } from '../dal/GeoDataHelper';
import { DailyViewModel } from '../viewmodel/DailyViewModel';
import { ListDailyAreaComponent } from '../view/ListDailyAreaComponent';
import { CommonConstants } from '../constants/CommonConstants';
import { ListHourlyAreaComponent } from '../view/ListHourlyAreaComponent';
import { AddressAreaComponent } from '../view/AddressAreaComponent'
import WeatherDataHelper from '../dal/WeatherDataHelper';
import hilog from '@ohos.hilog';
import { HttpGet } from '../util/HttpGet'
import { NowAreaComponent } from '../view/NowAreaComponent'
import { AqiAreaComponent } from '../view/AqiAreaComponent'
import { AqiViewModel } from '../viewmodel/AqiViewModel'
import { NowViewModel } from '../viewmodel/NowViewModel'
import { HourlyViewModel } from '../viewmodel/HourlyViewModel'
import { geoLocationManager } from '@kit.LocationKit';
import {checkPermissions} from '../util/PermissionGrant'
import {WeatherModel} from '../bean/WeatherModel'
import { promptAction } from '@kit.ArkUI';
import { BusinessError } from '@kit.BasicServicesKit';

/**
 * 主界面，由5个子视图组件组成：位置、当前天气、AQI信息、未来12小时预报和未来七天预报。
 */

const TAG='MainPage '
@Entry
@Component
struct MainPage {
//城市名称
@State city: string = ''
//Aqi组件数据模型
@State aqiViewModel: AqiViewModel = new AqiViewModel()
//当前天气组件数据模型
@State nowViewModel: NowViewModel = new NowViewModel()
//未来24小时组件数据模型
@State hourlyViewModel: Array<HourlyViewModel> =
  new Array<HourlyViewModel>();
//未来7天组件数据模型
@State dailyViewModel: Array<DailyViewModel> =
  new Array<DailyViewModel>();
  /**
   * 请求应用对应的权限
   */
   async  requestPermissions() {
    //stage版本
    //请求获取权限
    await checkPermissions().then((res:string)=>{
      console.log(TAG+'checkPermissions()返回的结果为：'+res)
    })
      .catch((err:string)=>{
        console.error(TAG+'checkPermissions()报错结果为：'+err)
      })
  }

async aboutToAppear() {
   await this.requestPermissions();
   console.log(TAG+'checkPermissions()返回的结果为：')
  //定义位置请求成功的回调函数
      let location=geoLocationManager.getLastLocation()
      console.info(TAG+'current location: ' + JSON.stringify(location));
      console.log(TAG+'正在获取城市位置信息----')
    if (location) {
      console.log(TAG+'成功')
     }else{
      console.log(TAG+'失败')
    }

  const locationChange = (async (location:geoLocationManager.Location)  => {
    let geoAddr=await GeoDataHelper.getAddr(location)
    //用于显示城市名称
    console.log(TAG+'城市信息:'+JSON.stringify(geoAddr))
    this.city = geoAddr[0].locality+(geoAddr[0].subLocality as string)
    console.log(TAG+'城市名称:'+JSON.stringify(this.city))
    //定义http请求工具
    let httpGet = new HttpGet()
    //获得字符串格式的经纬度
    let addressLL = GeoDataHelper.getLatLon(location)
    console.log(TAG+'城市经纬度:'+JSON.stringify(addressLL))
    //得到位置后，向天气预报接口发起网络请求
    await httpGet.doGet(addressLL).then((model) => {
      //得到天气数据后，进行封闭视图对应的数据模型。
      this.aqiViewModel = WeatherDataHelper.getAqiViewModel(model);
      this.nowViewModel = WeatherDataHelper.getNowViewModel(model)
      this.hourlyViewModel = WeatherDataHelper
        .getHourListDataSource(model)
      this.dailyViewModel = WeatherDataHelper
        .getDailyListDataSource(model)
    })
  })
  //请求位置数据
  GeoDataHelper.getGeoLocation(locationChange)
}

build() {
  Column({ space: CommonConstants.AREA_SPACE_SIZE }) {

    Row() {
      AddressAreaComponent({ city: $city })
    }.height("5%")

    Row() {
      NowAreaComponent({ nowViewModel: $nowViewModel })
    }.height("15%")

    Row() {
      AqiAreaComponent({ aqiViewModel: $aqiViewModel })
    }.height("15%")

    Row() {
      ListHourlyAreaComponent({ hourlyViewModel: $hourlyViewModel })
    }.height("12%")

    Row() {
      ListDailyAreaComponent({ dailyViewModel: $dailyViewModel })
    }
  }
  .height("100%")
  .alignItems(HorizontalAlign.Start)
  .backgroundImage($r('app.media.bg4'))
  .backgroundImageSize(ImageSize.Cover)
  .backgroundImagePosition(Alignment.Center)
  }
}
