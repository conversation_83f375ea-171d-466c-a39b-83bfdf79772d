import { HourlyViewModel } from '../viewmodel/HourlyViewModel';
import { ListHourlyItemComponent } from './ListHourlyItemComponent';
/**
 * 未来12小时天气视图组件。
 */
@Component
export struct ListHourlyAreaComponent {
  @Link hourlyViewModel: HourlyViewModel[]
  build() {
    Row() {
      List() {
        ForEach(this.hourlyViewModel, (item: HourlyViewModel) => {
          ListItem() {
            ListHourlyItemComponent({ itemInfo: item })
          }
        },( item:HourlyViewModel ) => JSON.stringify(item))
      }.listDirection(Axis.Horizontal)
    }
  }
}