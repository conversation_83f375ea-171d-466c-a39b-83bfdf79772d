import { router } from '@kit.ArkUI';

@Entry
@Component
struct Index {
  @State message: string = '天气应用导航';

  navigateToPage(url: string) {
    router.pushUrl({
      url: url
    }, router.RouterMode.Standard, (err) => {
      if (err) {
        console.log(`路由跳转失败，code is ${err.code}, message is ${err.message}`);
        return;
      }
      console.log('路由跳转成功');
    })
  }

  build() {
    Column({ space: 20 }) {
      Text(this.message)
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50, bottom: 30 })

      // 主应用按钮
      Button('天气应用主页')
        .width('80%')
        .height(50)
        .fontSize(18)
        .backgroundColor('#007DFF')
        .onClick(() => {
          this.navigateToPage('pages/MainPage');
        })

      // API测试按钮
      Button('API连接测试')
        .width('80%')
        .height(50)
        .fontSize(18)
        .backgroundColor('#FF6B35')
        .onClick(() => {
          this.navigateToPage('pages/Test/ApiTest');
        })

      // 调试页面按钮
      Button('UI组件调试')
        .width('80%')
        .height(50)
        .fontSize(18)
        .backgroundColor('#4CAF50')
        .onClick(() => {
          this.navigateToPage('pages/Test/DebugPage');
        })

      // 位置测试按钮
      Button('位置服务测试')
        .width('80%')
        .height(50)
        .fontSize(18)
        .backgroundColor('#9C27B0')
        .onClick(() => {
          this.navigateToPage('pages/Test/LocationKitTestPage');
        })

      // 位置诊断按钮
      Button('位置问题诊断')
        .width('80%')
        .height(50)
        .fontSize(18)
        .backgroundColor('#E91E63')
        .onClick(() => {
          this.navigateToPage('pages/Test/LocationDiagnostic');
        })

      // 说明文字
      Text('选择要测试的功能：\n\n• 天气应用主页：完整的天气应用\n• API连接测试：测试天气API是否正常\n• UI组件调试：使用模拟数据测试界面\n• 位置服务测试：测试位置权限和定位\n• 位置问题诊断：详细检查位置服务状态')
        .fontSize(14)
        .fontColor(Color.Gray)
        .margin({ top: 30, left: 20, right: 20 })
        .textAlign(TextAlign.Start)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}