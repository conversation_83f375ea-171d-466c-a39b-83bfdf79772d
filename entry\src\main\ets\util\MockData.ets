import { WeatherModel } from '../bean/WeatherModel';
import { Result1 } from '../bean/Result1';
import { Aqi } from '../bean/Aqi';
import { Daily } from '../bean/Daily';
import { Day } from '../bean/Day';
import { Night } from '../bean/Night';
import { Hourly } from '../bean/Hourly';

/**
 * 模拟天气数据，用于测试和API不可用时的备用方案
 */
export class MockData {
  
  /**
   * 获取模拟的天气数据
   */
  static getMockWeatherData(): WeatherModel {
    const mockModel = new WeatherModel();
    mockModel.status = 0;
    mockModel.msg = "ok";
    
    // 创建结果对象
    const result = new Result1();
    
    // 基本天气信息
    result.weather = "晴";
    result.temp = "25";
    result.templow = "18";
    result.temphigh = "28";
    result.humidity = "65";
    result.pressure = "1013";
    result.windspeed = "3";
    result.winddirect = "东南风";
    result.windpower = "3级";
    result.updatetime = new Date();

    // AQI信息
    const aqi = new Aqi();
    aqi.aqi = "45";
    aqi.quality = "优";
    aqi.timepoint = new Date();
    result.aqi = aqi;
    
    // 小时预报数据
    result.hourly = [];
    const hours = ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", 
                   "06:00", "07:00", "08:00", "09:00", "10:00", "11:00",
                   "12:00", "13:00", "14:00", "15:00", "16:00", "17:00",
                   "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"];
    const temps = ["18", "17", "16", "16", "17", "18", "20", "22", "24", "26", 
                   "27", "28", "28", "27", "26", "25", "24", "23", "22", "21", 
                   "20", "19", "19", "18"];
    const imgs = ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", 
                  "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", 
                  "0", "0", "0", "0"];
    
    for (let i = 0; i < 24; i++) {
      const hourly = new Hourly();
      hourly.time = hours[i];
      hourly.temp = temps[i];
      hourly.img = imgs[i];
      hourly.weather = "晴";
      result.hourly.push(hourly);
    }
    
    // 日预报数据
    result.daily = [];
    const weeks = ["今天", "明天", "后天", "周四", "周五", "周六", "周日"];
    const dayTemps = ["28", "29", "27", "26", "25", "24", "26"];
    const nightTemps = ["18", "19", "17", "16", "15", "14", "16"];
    const dayImgs = ["0", "1", "0", "2", "1", "0", "0"];
    const nightImgs = ["0", "0", "0", "0", "0", "0", "0"];
    
    for (let i = 0; i < 7; i++) {
      const daily = new Daily();
      daily.week = weeks[i];
      
      // 白天信息
      const day = new Day();
      day.weather = i % 2 === 0 ? "晴" : "多云";
      day.temphigh = dayTemps[i];
      day.img = dayImgs[i];
      day.windpower = "3级";
      day.winddirect = "东南风";
      daily.day = day;
      
      // 夜间信息
      const night = new Night();
      night.weather = "晴";
      night.templow = nightTemps[i];
      night.img = nightImgs[i];
      night.windpower = "2级";
      night.winddirect = "东风";
      daily.night = night;
      
      result.daily.push(daily);
    }
    
    mockModel.result = result;
    return mockModel;
  }
  
  /**
   * 检查是否应该使用模拟数据
   * 可以根据实际需要调整这个逻辑
   */
  static shouldUseMockData(): boolean {
    // 在开发阶段或API不可用时返回true
    // 如果您想直接使用模拟数据进行测试，请将下面的值改为 true
    return false; // 默认不使用模拟数据，可以根据需要修改
  }
}
