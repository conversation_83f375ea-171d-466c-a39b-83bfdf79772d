import { MockData } from '../../util/MockData';
import { WeatherDataHelper } from '../../dal/WeatherDataHelper';
import { NowAreaComponent } from '../../view/NowAreaComponent';
import { AqiAreaComponent } from '../../view/AqiAreaComponent';
import { AddressAreaComponent } from '../../view/AddressAreaComponent';
import { AqiViewModel } from '../../viewmodel/AqiViewModel';
import { NowViewModel } from '../../viewmodel/NowViewModel';
import { HourlyViewModel } from '../../viewmodel/HourlyViewModel';
import { DailyViewModel } from '../../viewmodel/DailyViewModel';
import { CommonConstants } from '../../constants/CommonConstants';

/**
 * 调试页面，用于测试天气组件显示
 */
@Entry
@Component
struct DebugPage {
  @State city: string = '北京市朝阳区'
  @State aqiViewModel: AqiViewModel = new AqiViewModel()
  @State nowViewModel: NowViewModel = new NowViewModel()
  @State hourlyViewModel: Array<HourlyViewModel> = new Array<HourlyViewModel>();
  @State dailyViewModel: Array<DailyViewModel> = new Array<DailyViewModel>();
  @State isLoaded: boolean = false;

  aboutToAppear() {
    this.loadMockData();
  }

  loadMockData() {
    try {
      const mockModel = MockData.getMockWeatherData();
      console.log('调试页面 - 加载模拟数据:', JSON.stringify(mockModel));

      // 处理数据
      this.aqiViewModel = WeatherDataHelper.getAqiViewModel(mockModel);
      this.nowViewModel = WeatherDataHelper.getNowViewModel(mockModel);
      this.hourlyViewModel = WeatherDataHelper.getHourListDataSource(mockModel);
      this.dailyViewModel = WeatherDataHelper.getDailyListDataSource(mockModel);

      this.isLoaded = true;
      console.log('调试页面 - 数据加载完成');
    } catch (error) {
      console.error('调试页面 - 加载数据失败:', JSON.stringify(error));
    }
  }

  build() {
    Column({ space: CommonConstants.AREA_SPACE_SIZE }) {
      // 标题
      Text('天气应用调试页面')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 20, bottom: 10 })

      if (this.isLoaded) {
        // 地址组件
        Row() {
          AddressAreaComponent({ city: $city })
        }.height("5%")

        // 当前天气组件
        Row() {
          NowAreaComponent({ nowViewModel: $nowViewModel })
        }.height("15%")

        // AQI组件
        Row() {
          AqiAreaComponent({ aqiViewModel: $aqiViewModel })
        }.height("15%")

        // 数据信息
        Column({ space: 10 }) {
          Text(`小时预报数据: ${this.hourlyViewModel.length} 条`)
            .fontSize(14)
            .fontColor(Color.Gray)

          Text(`日预报数据: ${this.dailyViewModel.length} 条`)
            .fontSize(14)
            .fontColor(Color.Gray)

          Text(`AQI: ${this.aqiViewModel.aqi} (${this.aqiViewModel.quality})`)
            .fontSize(14)
            .fontColor(Color.Gray)

          Text(`当前温度: ${this.nowViewModel.temp}`)
            .fontSize(14)
            .fontColor(Color.Gray)

          Text(`天气状况: ${this.nowViewModel.weather}`)
            .fontSize(14)
            .fontColor(Color.Gray)
        }
        .alignItems(HorizontalAlign.Start)
        .margin({ top: 20, horizontal: 20 })

      } else {
        Text('正在加载数据...')
          .fontSize(16)
          .margin({ top: 50 })
      }

      // 重新加载按钮
      Button('重新加载数据')
        .width('80%')
        .height(40)
        .margin({ top: 30 })
        .onClick(() => {
          this.isLoaded = false;
          this.loadMockData();
        })

      // 说明文字
      Text('此页面使用模拟数据测试UI组件显示效果')
        .fontSize(12)
        .fontColor(Color.Gray)
        .margin({ top: 20, horizontal: 20 })
        .textAlign(TextAlign.Center)
    }
    .width('100%')
    .height('100%')
    .alignItems(HorizontalAlign.Start)
    .backgroundImage($r('app.media.bg4'))
    .backgroundImageSize(ImageSize.Cover)
    .backgroundImagePosition(Alignment.Center)
  }
}
