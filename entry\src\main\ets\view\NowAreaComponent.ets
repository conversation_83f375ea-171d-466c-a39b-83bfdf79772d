import { NowViewModel } from '../viewmodel/NowViewModel';
import { CommonConstants } from '../constants/CommonConstants';
/**
 * 当前天气视图组件，包括天气、温度、最高温度和最低温度组成。
 */
@Component
export struct NowAreaComponent {
  @Link nowViewModel: NowViewModel
  build() {
    Column({ space: CommonConstants.SMALL_SPACE_SIZE }) {
      Row() {
        Text(this.nowViewModel.weather)
          .fontSize($r('app.float.common_font_size'))
      }
      Row() {
        Text(this.nowViewModel.temp)
          .fontSize($r('app.float.big_font_size'))
      }
      Row() {
        Column() {
          Text(this.nowViewModel.temphigh)
            .fontSize($r('app.float.common_font_size'))
        }.margin({
          right: $r('app.float.common_space_size')
        })
        Column() {
          Text(this.nowViewModel.templow)
            .fontSize($r('app.float.common_font_size'))
        }
      }
    }.width("100%")
  }
}