if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ListDailyAreaComponent_Params {
    dailyViewModel?: Array<DailyViewModel>;
}
import { ListDailyItemComponent } from "@normalized:N&&&entry/src/main/ets/view/ListDailyItemComponent&";
import type { DailyViewModel } from '../viewmodel/DailyViewModel';
export class ListDailyAreaComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__dailyViewModel = new SynchedPropertyObjectTwoWayPU(params.dailyViewModel, this, "dailyViewModel");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ListDailyAreaComponent_Params) {
    }
    updateStateVars(params: ListDailyAreaComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__dailyViewModel.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__dailyViewModel.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __dailyViewModel: SynchedPropertySimpleOneWayPU<Array<DailyViewModel>>;
    get dailyViewModel() {
        return this.__dailyViewModel.get();
    }
    set dailyViewModel(newValue: Array<DailyViewModel>) {
        this.__dailyViewModel.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(11:5)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(12:7)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(13:9)", "entry");
            Row.margin({
                bottom: { "id": 16777232, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" },
                left: { "id": 16777232, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" }
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("日期");
            Text.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(14:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
            Text.fontWeight(FontWeight.Bolder);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("天气");
            Text.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(18:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
            Text.fontWeight(FontWeight.Bolder);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("风力");
            Text.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(22:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
            Text.fontWeight(FontWeight.Bolder);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("最高");
            Text.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(26:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
            Text.fontWeight(FontWeight.Bolder);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("最低");
            Text.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(30:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
            Text.fontWeight(FontWeight.Bolder);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(39:9)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            List.create();
            List.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(40:11)", "entry");
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const item = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                        ListItem.debugLine("entry/src/main/ets/view/ListDailyAreaComponent.ets(42:15)", "entry");
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new ListDailyItemComponent(this, { itemInfo: item }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/view/ListDailyAreaComponent.ets", line: 43, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {
                                            itemInfo: item
                                        };
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                        itemInfo: item
                                    });
                                }
                            }, { name: "ListDailyItemComponent" });
                        }
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.dailyViewModel, forEachItemGenFunction, (item: DailyViewModel) => JSON.stringify(item), false, false);
        }, ForEach);
        ForEach.pop();
        List.pop();
        Row.pop();
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
