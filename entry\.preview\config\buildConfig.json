{"deviceType": "phone,tablet,2in1", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29900", "checkEntry": "true", "localPropertiesPath": "D:\\Code\\harmony\\Weather_Update2\\local.properties", "Path": "C:\\Program Files\\Huawei\\DevEco Studio\\tools\\node\\", "aceProfilePath": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "D:\\Code\\harmony\\Weather_Update2\\build-profile.json5", "watchMode": "true", "appResource": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\Code\\harmony\\Weather_Update2\\entry\\src\\main\\ets", "aceSoPath": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\Code\\harmony\\Weather_Update2\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/Index\",\"pages/MainPage\",\"pages/Test/LocationKitTestPage\"]}"]}}