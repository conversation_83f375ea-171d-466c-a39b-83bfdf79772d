if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface DebugPage_Params {
    city?: string;
    aqiViewModel?: AqiViewModel;
    nowViewModel?: NowViewModel;
    hourlyViewModel?: Array<HourlyViewModel>;
    dailyViewModel?: Array<DailyViewModel>;
    isLoaded?: boolean;
}
import { MockData } from "@normalized:N&&&entry/src/main/ets/util/MockData&";
import { WeatherDataHelper } from "@normalized:N&&&entry/src/main/ets/dal/WeatherDataHelper&";
import { NowAreaComponent } from "@normalized:N&&&entry/src/main/ets/view/NowAreaComponent&";
import { AqiAreaComponent } from "@normalized:N&&&entry/src/main/ets/view/AqiAreaComponent&";
import { AddressAreaComponent } from "@normalized:N&&&entry/src/main/ets/view/AddressAreaComponent&";
import { AqiViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/AqiViewModel&";
import { NowViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/NowViewModel&";
import type { HourlyViewModel } from '../../viewmodel/HourlyViewModel';
import type { DailyViewModel } from '../../viewmodel/DailyViewModel';
import { CommonConstants } from "@normalized:N&&&entry/src/main/ets/constants/CommonConstants&";
class DebugPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__city = new ObservedPropertySimplePU('北京市朝阳区', this, "city");
        this.__aqiViewModel = new ObservedPropertyObjectPU(new AqiViewModel(), this, "aqiViewModel");
        this.__nowViewModel = new ObservedPropertyObjectPU(new NowViewModel(), this, "nowViewModel");
        this.__hourlyViewModel = new ObservedPropertyObjectPU(new Array<HourlyViewModel>(), this, "hourlyViewModel");
        this.__dailyViewModel = new ObservedPropertyObjectPU(new Array<DailyViewModel>(), this, "dailyViewModel");
        this.__isLoaded = new ObservedPropertySimplePU(false, this, "isLoaded");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: DebugPage_Params) {
        if (params.city !== undefined) {
            this.city = params.city;
        }
        if (params.aqiViewModel !== undefined) {
            this.aqiViewModel = params.aqiViewModel;
        }
        if (params.nowViewModel !== undefined) {
            this.nowViewModel = params.nowViewModel;
        }
        if (params.hourlyViewModel !== undefined) {
            this.hourlyViewModel = params.hourlyViewModel;
        }
        if (params.dailyViewModel !== undefined) {
            this.dailyViewModel = params.dailyViewModel;
        }
        if (params.isLoaded !== undefined) {
            this.isLoaded = params.isLoaded;
        }
    }
    updateStateVars(params: DebugPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__city.purgeDependencyOnElmtId(rmElmtId);
        this.__aqiViewModel.purgeDependencyOnElmtId(rmElmtId);
        this.__nowViewModel.purgeDependencyOnElmtId(rmElmtId);
        this.__hourlyViewModel.purgeDependencyOnElmtId(rmElmtId);
        this.__dailyViewModel.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoaded.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__city.aboutToBeDeleted();
        this.__aqiViewModel.aboutToBeDeleted();
        this.__nowViewModel.aboutToBeDeleted();
        this.__hourlyViewModel.aboutToBeDeleted();
        this.__dailyViewModel.aboutToBeDeleted();
        this.__isLoaded.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __city: ObservedPropertySimplePU<string>;
    get city() {
        return this.__city.get();
    }
    set city(newValue: string) {
        this.__city.set(newValue);
    }
    private __aqiViewModel: ObservedPropertyObjectPU<AqiViewModel>;
    get aqiViewModel() {
        return this.__aqiViewModel.get();
    }
    set aqiViewModel(newValue: AqiViewModel) {
        this.__aqiViewModel.set(newValue);
    }
    private __nowViewModel: ObservedPropertyObjectPU<NowViewModel>;
    get nowViewModel() {
        return this.__nowViewModel.get();
    }
    set nowViewModel(newValue: NowViewModel) {
        this.__nowViewModel.set(newValue);
    }
    private __hourlyViewModel: ObservedPropertyObjectPU<Array<HourlyViewModel>>;
    get hourlyViewModel() {
        return this.__hourlyViewModel.get();
    }
    set hourlyViewModel(newValue: Array<HourlyViewModel>) {
        this.__hourlyViewModel.set(newValue);
    }
    private __dailyViewModel: ObservedPropertyObjectPU<Array<DailyViewModel>>;
    get dailyViewModel() {
        return this.__dailyViewModel.get();
    }
    set dailyViewModel(newValue: Array<DailyViewModel>) {
        this.__dailyViewModel.set(newValue);
    }
    private __isLoaded: ObservedPropertySimplePU<boolean>;
    get isLoaded() {
        return this.__isLoaded.get();
    }
    set isLoaded(newValue: boolean) {
        this.__isLoaded.set(newValue);
    }
    aboutToAppear() {
        this.loadMockData();
    }
    loadMockData() {
        try {
            const mockModel = MockData.getMockWeatherData();
            console.log('调试页面 - 加载模拟数据:', JSON.stringify(mockModel));
            // 处理数据
            this.aqiViewModel = WeatherDataHelper.getAqiViewModel(mockModel);
            this.nowViewModel = WeatherDataHelper.getNowViewModel(mockModel);
            this.hourlyViewModel = WeatherDataHelper.getHourListDataSource(mockModel);
            this.dailyViewModel = WeatherDataHelper.getDailyListDataSource(mockModel);
            this.isLoaded = true;
            console.log('调试页面 - 数据加载完成');
        }
        catch (error) {
            console.error('调试页面 - 加载数据失败:', JSON.stringify(error));
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: CommonConstants.AREA_SPACE_SIZE });
            Column.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(48:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.alignItems(HorizontalAlign.Start);
            Column.backgroundImage({ "id": 16777227, "type": 20000, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Column.backgroundImageSize(ImageSize.Cover);
            Column.backgroundImagePosition(Alignment.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题
            Text.create('天气应用调试页面');
            Text.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(50:7)", "entry");
            // 标题
            Text.fontSize(20);
            // 标题
            Text.fontWeight(FontWeight.Bold);
            // 标题
            Text.margin({ top: 20, bottom: 10 });
        }, Text);
        // 标题
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoaded) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 地址组件
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(57:9)", "entry");
                        // 地址组件
                        Row.height("5%");
                    }, Row);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new AddressAreaComponent(this, { city: this.__city }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Test/DebugPage.ets", line: 58, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        city: this.city
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "AddressAreaComponent" });
                    }
                    // 地址组件
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 当前天气组件
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(62:9)", "entry");
                        // 当前天气组件
                        Row.height("15%");
                    }, Row);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new NowAreaComponent(this, { nowViewModel: this.__nowViewModel }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Test/DebugPage.ets", line: 63, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        nowViewModel: this.nowViewModel
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "NowAreaComponent" });
                    }
                    // 当前天气组件
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // AQI组件
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(67:9)", "entry");
                        // AQI组件
                        Row.height("15%");
                    }, Row);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new AqiAreaComponent(this, { aqiViewModel: this.__aqiViewModel }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Test/DebugPage.ets", line: 68, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        aqiViewModel: this.aqiViewModel
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "AqiAreaComponent" });
                    }
                    // AQI组件
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 数据信息
                        Column.create({ space: 10 });
                        Column.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(72:9)", "entry");
                        // 数据信息
                        Column.alignItems(HorizontalAlign.Start);
                        // 数据信息
                        Column.margin({ top: 20, left: 20, right: 20 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`小时预报数据: ${this.hourlyViewModel.length} 条`);
                        Text.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(73:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor(Color.Gray);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`日预报数据: ${this.dailyViewModel.length} 条`);
                        Text.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(77:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor(Color.Gray);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`AQI: ${this.aqiViewModel.aqi} (${this.aqiViewModel.quality})`);
                        Text.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(81:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor(Color.Gray);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`当前温度: ${this.nowViewModel.temp}`);
                        Text.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(85:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor(Color.Gray);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`天气状况: ${this.nowViewModel.weather}`);
                        Text.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(89:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor(Color.Gray);
                    }, Text);
                    Text.pop();
                    // 数据信息
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正在加载数据...');
                        Text.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(97:9)", "entry");
                        Text.fontSize(16);
                        Text.margin({ top: 50 });
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 重新加载按钮
            Button.createWithLabel('重新加载数据');
            Button.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(103:7)", "entry");
            // 重新加载按钮
            Button.width('80%');
            // 重新加载按钮
            Button.height(40);
            // 重新加载按钮
            Button.margin({ top: 30 });
            // 重新加载按钮
            Button.onClick(() => {
                this.isLoaded = false;
                this.loadMockData();
            });
        }, Button);
        // 重新加载按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 说明文字
            Text.create('此页面使用模拟数据测试UI组件显示效果');
            Text.debugLine("entry/src/main/ets/pages/Test/DebugPage.ets(113:7)", "entry");
            // 说明文字
            Text.fontSize(12);
            // 说明文字
            Text.fontColor(Color.Gray);
            // 说明文字
            Text.margin({ top: 20, left: 20, right: 20 });
            // 说明文字
            Text.textAlign(TextAlign.Center);
        }, Text);
        // 说明文字
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "DebugPage";
    }
}
registerNamedRoute(() => new DebugPage(undefined, {}), "", { bundleName: "com.example.weather", moduleName: "entry", pagePath: "pages/Test/DebugPage", pageFullPath: "entry/src/main/ets/pages/Test/DebugPage", integratedHsp: "false", moduleType: "followWithHap" });
