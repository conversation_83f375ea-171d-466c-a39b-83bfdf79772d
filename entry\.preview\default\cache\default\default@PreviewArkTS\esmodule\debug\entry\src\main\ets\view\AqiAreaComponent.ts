if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AqiAreaComponent_Params {
    aqiViewModel?: AqiViewModel;
}
import type { AqiViewModel } from '../viewmodel/AqiViewModel';
export class AqiAreaComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__aqiViewModel = new SynchedPropertyObjectTwoWayPU(params.aqiViewModel, this, "aqiViewModel");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AqiAreaComponent_Params) {
    }
    updateStateVars(params: AqiAreaComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__aqiViewModel.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__aqiViewModel.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __aqiViewModel: SynchedPropertySimpleOneWayPU<AqiViewModel>;
    get aqiViewModel() {
        return this.__aqiViewModel.get();
    }
    set aqiViewModel(newValue: AqiViewModel) {
        this.__aqiViewModel.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(10:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(11:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(12:9)", "entry");
            Column.layoutWeight(3);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("A Q I ：");
            Text.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(13:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(16:9)", "entry");
            Column.layoutWeight(4);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create({ alignContent: Alignment.Center });
            Stack.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(17:11)", "entry");
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Gauge.create({ value: this.aqiViewModel.aqi, min: 0, max: 500 });
            Gauge.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(18:13)", "entry");
            Gauge.startAngle(210);
            Gauge.endAngle(150);
            Gauge.colors([[0x00FF00, 0.1], [0xFFFF00, 0.1],
                [0xFF6100, 0.1], [0xFF0000, 0.1], [0xA020F0, 0.2],
                [0x8B0000, 0.4]]);
            Gauge.strokeWidth(15);
        }, Gauge);
        Gauge.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.aqiViewModel.aqi.toString());
            Text.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(26:13)", "entry");
            Text.fontSize({ "id": 16777230, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Stack.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(30:9)", "entry");
            Column.layoutWeight(3);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.aqiViewModel.quality);
            Text.debugLine("entry/src/main/ets/view/AqiAreaComponent.ets(31:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
