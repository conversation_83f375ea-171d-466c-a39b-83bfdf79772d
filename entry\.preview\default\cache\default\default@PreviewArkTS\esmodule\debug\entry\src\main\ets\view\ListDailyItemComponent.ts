if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ListDailyItemComponent_Params {
    itemInfo?: DailyViewModel;
}
import type { DailyViewModel } from '../viewmodel/DailyViewModel';
export class ListDailyItemComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__itemInfo = new SynchedPropertyObjectOneWayPU(params.itemInfo, this, "itemInfo");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ListDailyItemComponent_Params) {
    }
    updateStateVars(params: ListDailyItemComponent_Params) {
        this.__itemInfo.reset(params.itemInfo);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__itemInfo.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__itemInfo.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __itemInfo: SynchedPropertySimpleOneWayPU<DailyViewModel>;
    get itemInfo() {
        return this.__itemInfo.get();
    }
    set itemInfo(newValue: DailyViewModel) {
        this.__itemInfo.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/ListDailyItemComponent.ets(9:5)", "entry");
            Row.width("100%");
            Row.margin({
                bottom: { "id": 16777232, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" },
                left: { "id": 16777232, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" }
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.itemInfo.week);
            Text.debugLine("entry/src/main/ets/view/ListDailyItemComponent.ets(10:7)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": -1, "type": 30000, params: [this.itemInfo.img], "bundleName": "com.example.weather", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/view/ListDailyItemComponent.ets(13:7)", "entry");
            Image.objectFit(ImageFit.Contain);
            Image.width({ "id": 16777233, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Image.height({ "id": 16777233, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Image.layoutWeight(1);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.itemInfo.windpower);
            Text.debugLine("entry/src/main/ets/view/ListDailyItemComponent.ets(18:7)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.itemInfo.temphigh);
            Text.debugLine("entry/src/main/ets/view/ListDailyItemComponent.ets(21:7)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.itemInfo.templow);
            Text.debugLine("entry/src/main/ets/view/ListDailyItemComponent.ets(24:7)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
