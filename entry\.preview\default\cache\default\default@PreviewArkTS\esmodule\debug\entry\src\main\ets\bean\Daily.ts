import { Day } from "@normalized:N&&&entry/src/main/ets/bean/Day&";
import { Night } from "@normalized:N&&&entry/src/main/ets/bean/Night&";
/**
 * 每个字段的解释参考 https://www.jisuapi.com/api/weather
 */
export class Daily {
    public date: Date = new Date();
    public week: string = '';
    public sunrise: string = '';
    public sunset: string = '';
    public night: Night = new Night();
    public day: Day = new Day();
}
