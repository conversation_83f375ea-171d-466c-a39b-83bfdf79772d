[鸿蒙分析-天气查询代码理解【中工开发者】](https://harmonyosdev.csdn.net/67fe0646c89bb164987fa3c0.html)
===================================================================================

源代码为课上资源weather代码。

天气开发：在开发天气应用项目时，我们旨在为用户提供一个直观、易用且功能丰富的平台，以便实时获取全球各地的天气信息。本项目将集成高精度的天气数据API，通过友好的用户界面展示当前天气状况、未来预报、空气质量指数等关键信息。此外，应用将支持多语言和多城市查询，以满足不同用户的需求。通过本项目，我们期望提升用户的日常生活质量，帮助他们更好地规划日常活动和出行计划。

下面是部分项目的简单介绍。

数据处理：

进行天气查询，首先需要获取实时天气数据，然后对天气数据进行分析。通过天气服务提供商的API获取实时天气信息，需要使用网络库如Retrofit来简化HTTP请求，该例子采用的提娜齐预报链接数据为[全国天气预报API接口\_免费数据接口 - 极速数据](https://link.csdn.net/?target=https%3A%2F%2Fwww.jisuapi.com%2Fapi%2Fweather%3Flogin%3Dfrom_csdn "全国天气预报API接口_免费数据接口 - 极速数据")的数据，进行开发时需要注册信息。

数据处理：

```
import { Aqiinfo } from './Aqiinfo'

/**
 * 每个字段的解释参考 https://www.jisuapi.com/api/weather
 */
export class Aqi {
  public so2: string = '';
  public so224: string = '';
  public no2: string = '';
  public no224: string = '';
  public co: string = '';
  public co24: string = '';
  public o3: string = '';
  public o38: string = '';
  public o324: string = '';
  public pm10: string = '';
  public pm1024: string = '';
  public pm2_5: string = '';
  public pm2_524: string = '';
  public iso2: string = '';
  public ino2: string = '';
  public ico: string = '';
  public io3: string = '';
  public io38: string = '';
  public ipm10: string = '';
  public ipm2_5: string = '';
  public aqi: string = '';
  public primarypollutant: string = '';
  public quality: string = '';
  public timepoint: Date = new Date();
  public aqiinfo: Aqiinfo = new Aqiinfo();
}
```

先对目录进行分析：

上图展示了一个HarmonyOS天气应用项目的文件目录结构。描述如下：

1.  **项目根目录**：包含整个应用的源代码和配置文件。
    
2.  **配置和构建相关**：
    
    *   `.hvigor` 和 `.hvigorfile.ts`：项目构建配置。
    *   `.idea`：IDE项目配置文件夹。
    *   `.gitignore`：Git忽略文件配置。
    *   `build-profile.json5`：构建环境配置文件。
3.  **应用入口和构建输出**：
    
    *   `entry`：应用的入口点，包含预览(`.preview`)和构建(`build`)文件夹。
4.  **源代码**：
    
    *   `src/main/ets`：应用的主要源代码，使用ETS语言编写。
        *   `bean`：数据模型。
        *   `constants`：常量定义。
        *   `dal`：数据访问层。
        *   `entryability`：应用入口功能。
        *   `pages`：页面组件。
        *   `util`：工具类。
        *   `view` 和 `viewmodel`：用户界面和视图逻辑。
5.  **资源文件**：
    
    *   `resources`：存放应用资源。
        *   `base`、`element`、`media`、`profile`：资源分类。
        *   `en_US` 和 `zh_CN`：多语言资源。
        *   `rawfile`：原始资源文件。
6.  **测试相关**：
    
    *   `mock`：模拟数据或服务。
    *   `ohosTest`：HarmonyOS平台测试代码。
    *   `test`：通用测试代码，包含测试文件(`List.test.ets`, `LocalUnit.test.ets`)。
7.  **模块配置**：
    
    *   `module.json5`：定义模块的元数据和依赖，等。

功能实现：

```
/**
 * 定义应用中使用的一些常量
 */
import { Permissions } from '@ohos.abilityAccessCtrl';

export class CommonConstants {
  //应用的app_code，这个需要自行申请
  static readonly APP_CODE: string = "75110adfeabe4404ae7bb2ba2e90f7a9"
  //访问天气预报的API地址
  static readonly WEATHER_URL: string =
    "http://jisutqybmf.market.alicloudapi.com/weather/query"
  //视图组件中不同组件的小间距
  static readonly SMALL_SPACE_SIZE = 8
  //主界面中不同区域的大间距
  static readonly AREA_SPACE_SIZE: number = 20;
  //返回的天气预报有24小时的数据
  static readonly HOURLY_LIST_SIZE: number = 24;
  //返回的天气预报有7天的数据
  static readonly DAILY_LIST_SIZE: number = 7;
  // 位置权限
  static readonly REQUEST_PERMISSIONS: Array<Permissions> =
    [
      'ohos.permission.APPROXIMATELY_LOCATION',
      'ohos.permission.LOCATION'
    ];
  static readonly cityCodeForSelected: Array<string> = [
    "101180101", // 郑州的citycode
    "101010100", // 北京的citycode
    "101020100"  // 上海的citycode
  ]
  static readonly cityNameForSelected: Array<SelectOption> = [
    { value: '郑州' },
    { value: '北京' },
    { value: '上海' }
  ]
}
```

代码解释：

`CommonConstants` 类在天气应用中定义了一系列关键常量，以供应用各部分统一使用。`APP_CODE` 是用于API访问的身份验证码，需要开发者自行申请。`WEATHER_URL` 是获取天气数据的API地址，但代码中的URL字符串似乎不完整，末尾的引号缺失，这可能是一个错误。`SMALL_SPACE_SIZE` 和 `AREA_SPACE_SIZE` 分别定义了视图组件中小间距和主界面中大间距的尺寸，用于布局设计，数值分别为8和20。`HOURLY_LIST_SIZE` 和 `DAILY_LIST_SIZE` 分别表示天气预报中24小时和7天的数据条目数。`REQUEST_PERMISSIONS` 包含了应用所需的位置权限，包括大致位置权限 `ohos.permission.APPROXIMATELY_LOCATION` 和精确位置权限 `ohos.permission.LOCATION`。`cityCodeForSelected` 和 `cityNameForSelected` 分别是预选城市的citycode和名称数组，目前包括郑州、北京和上海。这些常量有助于保持代码的一致性和易于管理

这个常量类为天气应用提供了一些基础的配置信息，包括API访问地址、界面布局的间距、天气预报数据的条目数以及位置权限等。这些常量可以在应用的不同部分被引用，以保持代码的一致性和易于管理。

生成代码视图：

```
class WeatherDataHelper {
  /**
   * 返回城市代码
   * @param model 天气数据对象
   */
  getCity(model: WeatherModel) {
    return model.result.city
  }

  /**
   * 生成AQI区域的数据模型
   * @param model 天气数据对象
   */
  getAqiViewModel(model: WeatherModel) {
    let viewmodel = new AqiViewModel();
    viewmodel.aqi = Number.parseInt(model.result.aqi.aqi)
    viewmodel.quality = model.result.aqi.quality
    return viewmodel
  }

  /**
   * 生成当前天气区域的数据模型
   * @param model 天气数据对象
   */
  getNowViewModel(model: WeatherModel) {
    let viewmodel = new NowViewModel();
    viewmodel.weather = model.result.weather
    viewmodel.temp = model.result.temp + "℃"
    viewmodel.templow = "最低" + model.result.templow + "℃"
    viewmodel.temphigh = "最高" + model.result.temphigh + "℃"
    return viewmodel
  }

  /**
   * 生成未来12小时天气区域的数据模型
   * @param model 天气数据对象
   */
  getHourListDataSource(model: WeatherModel): Array<HourlyViewModel> {
    let listItems: Array<HourlyViewModel> = [];
    for (let i = 0; i < CommonConstants.HOURLY_LIST_SIZE; i++) {
      let itemInfo: HourlyViewModel = new HourlyViewModel();
      itemInfo.temp = model.result.hourly[i].temp + "℃";
      itemInfo.img = "weather/" + model.result.hourly[i].img + ".png" ;
      let hour:number = Number.parseInt( model.result.hourly[i].time.split(':')[0] ) ;
      itemInfo.time = TimeFormat.formatAMPM(hour)
      listItems.push(itemInfo);
    }
    return listItems;
  }

  /**
   * 生成未来七天天气区域的数据模型
   * @param model 天气数据对象
   */
  getDailyListDataSource(model: WeatherModel): Array<DailyViewModel> {
    let listItems: Array<DailyViewModel> = [];
    for (let i = 0; i < CommonConstants.DAILY_LIST_SIZE; i++) {
      let itemInfo: DailyViewModel = new DailyViewModel();
      itemInfo.week = model.result.daily[i].week
      itemInfo.img = "weather/" + model.result.daily[i].day.img + ".png"
      itemInfo.windpower = model.result.daily[i].day.windpower
      itemInfo.templow = model.result.daily[i].night.templow + "℃"
      itemInfo.temphigh = model.result.daily[i].day.temphigh + "℃"
      listItems.push(itemInfo);
    }
    return listItems;
  }
}

let weatherDataHelper = new WeatherDataHelper();

export default weatherDataHelper as WeatherDataHelper;
```

代码理解：

WeatherDataHelper 类是一个专门用于处理和转换天气数据的辅助类，它包含了一系列方法，用于将后端提供的天气数据模型（WeatherModel）转换为前端视图模型，以便在用户界面中展示。类中的方法包括：getCity 用于获取城市代码；getAqiViewModel 用于生成包含空气质量指数（AQI）和空气质量等级的视图模型；getNowViewModel 用于生成包含当前天气状况、温度、最低温度和最高温度的视图模型；getHourListDataSource 用于生成未来12小时每小时天气数据的视图模型数组；getDailyListDataSource 用于生成未来七天每日天气数据的视图模型数组。此外，代码中还创建了一个 weatherDataHelper 实例，并将其作为默认导出，使得其他模块可以通过导入 WeatherDataHelper 来使用这个实例。这样的设计有助于实现数据层与表示层的解耦，提高代码的模块化和可维护性。

下图为代码运行截图：

主要代码：

状态变量：

```
struct Index {
  @State city: string = ''
  @State aqiViewModel: AqiViewModel = new AqiViewModel()
  @State nowViewModel: NowViewModel = new NowViewModel()
  @State hourlyViewModel: Array<HourlyViewModel> =
    new Array<HourlyViewModel>();
  @State dailyViewModel: Array<DailyViewModel> =
    new Array<DailyViewModel>();
  @State latitude: number = 0; 
  @State longitude: number = 0;
  cityCodeForSelected: Array<string> =
    CommonConstants.cityCodeForSelected
  @State cityNameForSelected: Array<SelectOption> =
    CommonConstants.cityNameForSelected
  private context: common.UIAbilityContext =
    getContext(this) as common.UIAbilityContext;
  private httpGet: HttpGet = new HttpGet();
```

city：城市名称，初始化为空字符串。aqiViewModel：AQI（空气质量指数）组件的数据模型。nowViewModel：当前天气组件的数据模型。hourlyViewModel：未来24小时天气组件的数据模型，初始化为空数组。dailyViewModel：未来7天天气组件的数据模型，初始化为空数组。latitude 和 longitude：存储当前的纬度和经度。cityCodeForSelected 和 cityNameForSelected：预选城市的编码和名称数组，使用 CommonConstants 中的值。context：上下文对象，用于获取当前的UIAbilityContext。httpGet：用于HTTP请求的实例。

方法构造：

获取当前位置的天气信息

```
getCurrentLocationWeather() {
    if( ! geoLocationManager.isLocationEnabled() )
    {
      promptAction.showToast({
        message: '请打开设备位置信息开关',
        duration: 2000
      })
      return;
    }
    // 获得设备当前位置
    let location = geoLocationManager.getCurrentLocation()
    location.then((value) => {
      if( value==null )
        return;
        // 转换以下位置经纬度格式
        let addressLL = geoDataHelper.getLatLon(value)
        // 根据当前位置请求天气信息
        this.httpGet.doGetWithLocation(addressLL, (data) => {
          this.updateUI(data)
        })
    })
  }
```

```
响应不同城市按钮
```

```
selectSelectedHandler(index:number) {
    this.httpGet.doGetWithCitycode(
      this.cityCodeForSelected[index], (data) => {
      this.updateUI(data);
    })
  }
```

```
更新根据请求获得天气数据，更新界面
```

```
updateUI(data:string) {
    // 定义天气模型
    let model: WeatherModel
    //对返回的字符串转化成WeatherModel类型的对象
    model = JSON.parse(data)
    // 如果返回天气信息
    if (model.result.toString() != "") {
      // 以下是根据model更新状态量，进而自动刷新界面
      this.city = weatherDataHelper.getCity(model)
      this.aqiViewModel = weatherDataHelper.getAqiViewModel(model);
      this.nowViewModel = weatherDataHelper.getNowViewModel(model)
      this.hourlyViewModel = weatherDataHelper
        .getHourListDataSource(model)
      this.dailyViewModel = weatherDataHelper
        .getDailyListDataSource(model)
    }
    else {
      promptAction.showToast({
        message: '获取天气信息失败，可能是请求次数用尽',
        duration: 3000,
      });
    }
  }
```

onPageShow()：当页面显示时调用，请求位置权限，如果权限被授予，则调用 getCurrentLocationWeather() 方法获取当前位置的天气信息。getCurrentLocationWeather()：检查位置服务是否启用，如果启用，则获取当前位置并请求天气信息。selectSelectedHandler(index)：响应城市选择事件，根据选中的城市编码请求天气信息。updateUI(data)：更新界面的方法，将返回的JSON数据解析为 WeatherModel 对象，并更新状态变量，从而触发界面刷新。build()：构建用户界面的方法，使用 Column 和 Row 组件组织页面布局，包括地址区域、当前天气、AQI、未来小时和未来天气列表。

```
getCurrentLocationWeather() {
    if( ! geoLocationManager.isLocationEnabled() )
    {
      promptAction.showToast({
        message: '请打开设备位置信息开关',
        duration: 2000
      })
      return;
    }
    // 获得设备当前位置
    let location = geoLocationManager.getCurrentLocation()
    location.then((value) => {
      if( value==null )
        return;
        // 转换以下位置经纬度格式
        let addressLL = geoDataHelper.getLatLon(value)
        // 根据当前位置请求天气信息
        this.httpGet.doGetWithLocation(addressLL, (data) => {
          this.updateUI(data)
        })
    })
  }
```

这段代码是一个名为 `getCurrentLocationWeather` 的方法，它的作用是获取设备当前的位置并根据这个位置请求天气信息。

1.  **检查位置服务是否启用**：通过调用 `geoLocationManager.isLocationEnabled()` 检查设备的位置服务是否已经开启。如果没有开启，将使用 `promptAction.showToast` 显示一个提示信息，告知用户需要打开设备的位置信息开关，并在2秒后自动消失。如果位置服务未开启，方法将返回，不再继续执行。
2.  **获取当前位置**：如果位置服务已启用，通过调用 `geoLocationManager.getCurrentLocation()` 获取设备当前的地理位置信息。这是一个异步操作，返回一个 `Promise` 对象。
3.  **处理位置信息**：在 `Promise` 的 `then` 方法中处理获取到的位置信息。如果 `value` 为 `null`，表示未能成功获取位置信息，此时方法将返回，不再继续执行。
4.  **转换位置格式**：如果成功获取位置信息，使用 `geoDataHelper.getLatLon(value)` 将位置信息转换为经纬度格式，存储在 `addressLL` 变量中。
5.  **请求天气信息**：使用 `this.httpGet.doGetWithLocation(addressLL, (data) => {...})` 根据获取到的经纬度向天气服务发送HTTP GET请求，请求当前位置的天气信息。这里的 `doGetWithLocation` 方法可能是一个自定义方法，用于执行HTTP请求。
6.  **更新****UI**：当HTTP请求完成后，回调函数将被调用，并将返回的数据作为参数传递给 `this.updateUI(data)` 方法。`updateUI` 方法将处理这些数据，并更新应用的UI，以显示当前位置的天气信息。

这个方法体现了一个典型的异步编程模式，通过Promise处理异步获取的位置信息，并在获取成功后进行后续的天气信息请求和UI更新。同时，它也处理了位置服务未开启的情况，提高了应用的健壮性。

```
updateUI(data:string) {
    let model: WeatherModel
    model = JSON.parse(data)
    if (model.result.toString() != "") {
      this.city = weatherDataHelper.getCity(model)
      this.aqiViewModel = weatherDataHelper.getAqiViewModel(model);
      this.nowViewModel = weatherDataHelper.getNowViewModel(model)
      this.hourlyViewModel = weatherDataHelper
        .getHourListDataSource(model)
      this.dailyViewModel = weatherDataHelper
        .getDailyListDataSource(model)
    }
    else {
      promptAction.showToast({
        message: '获取天气信息失败，可能是请求次数用尽',
        duration: 3000,
      });
```

这段代码定义了一个名为 updateUI 的方法，它的作用是更新用户界面（UI）以显示最新的天气信息。以下是该方法的详细步骤：

1.  **定义天气模型**：声明一个 WeatherModel 类型的变量 model，用于存储解析后的天气数据。
2.  **解析****JSON****数据**：使用 JSON.parse(data) 将传入的字符串 data 解析为 WeatherModel 类型的对象。这里假设 data 是一个JSON格式的字符串，包含了天气信息。
3.  **检查天气信息**：通过 model.result.toString() != "" 检查解析后的天气数据是否有效。如果 model.result 不是一个空字符串，说明天气信息已成功获取。
4.  **更新状态量**：如果天气信息有效，使用 weatherDataHelper 实例调用不同的方法来更新应用的状态量：
    *   this.city 更新为城市名称，通过 weatherDataHelper.getCity(model) 获取。
    *   this.aqiViewModel 更新为AQI组件的数据模型，通过 weatherDataHelper.getAqiViewModel(model) 获取。
    *   this.nowViewModel 更新为当前天气组件的数据模型，通过 weatherDataHelper.getNowViewModel(model) 获取。
    *   this.hourlyViewModel 更新为未来24小时天气组件的数据模型数组，通过 weatherDataHelper.getHourListDataSource(model) 获取。
    *   this.dailyViewModel 更新为未来7天天气组件的数据模型数组，通过 weatherDataHelper.getDailyListDataSource(model) 获取。
5.  **自动刷新界面**：由于状态量使用了响应式编程（如Vue.js或类似框架）的 @State 装饰器，更新状态量将自动触发界面的重新渲染。
6.  **错误处理**：如果 model.result 是一个空字符串，说明天气信息获取失败。此时，使用 promptAction.showToast 显示一个提示信息，告知用户获取天气信息失败，可能是由于请求次数用尽，并在3秒后自动消失。

这个方法体现了MVVM（Model-View-ViewModel）架构中ViewModel层的作用，即处理数据并更新UI。通过解析天气数据并更新状态量，界面将自动显示最新的天气信息。同时，它也处理了数据获取失败的情况，提高了应用的健壮性。

代码运行样例：

推荐内容

\>由 \[Circle 阅读助手\](https://circlereader.com) 生成