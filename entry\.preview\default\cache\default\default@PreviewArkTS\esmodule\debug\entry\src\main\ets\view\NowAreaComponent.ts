if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface NowAreaComponent_Params {
    nowViewModel?: NowViewModel;
}
import type { NowViewModel } from '../viewmodel/NowViewModel';
import { CommonConstants } from "@normalized:N&&&entry/src/main/ets/constants/CommonConstants&";
export class NowAreaComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__nowViewModel = new SynchedPropertyObjectTwoWayPU(params.nowViewModel, this, "nowViewModel");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: NowAreaComponent_Params) {
    }
    updateStateVars(params: NowAreaComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__nowViewModel.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__nowViewModel.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __nowViewModel: SynchedPropertySimpleOneWayPU<NowViewModel>;
    get nowViewModel() {
        return this.__nowViewModel.get();
    }
    set nowViewModel(newValue: NowViewModel) {
        this.__nowViewModel.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: CommonConstants.SMALL_SPACE_SIZE });
            Column.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(10:5)", "entry");
            Column.width("100%");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(11:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.nowViewModel.weather);
            Text.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(12:9)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(15:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.nowViewModel.temp);
            Text.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(16:9)", "entry");
            Text.fontSize({ "id": 16777230, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(19:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(20:9)", "entry");
            Column.margin({
                right: { "id": 16777232, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.nowViewModel.temphigh);
            Text.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(21:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(26:9)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.nowViewModel.templow);
            Text.debugLine("entry/src/main/ets/view/NowAreaComponent.ets(27:11)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
