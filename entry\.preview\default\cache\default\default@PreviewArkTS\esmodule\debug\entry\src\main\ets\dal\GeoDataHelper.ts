import hilog from "@ohos:hilog";
import geoLocationManager from "@ohos:geoLocationManager";
import type { Callback as Callback } from "@ohos:base";
const TAG = 'GeoDataHelper ';
/**
 *地理位置数据辅助类
 */
export class GeoDataHelper {
    /**
     * 返回当前地理位置
     * @param locationChange  回调函数
     */
    static async getGeoLocation(locationChange: Callback<geoLocationManager.Location>) {
        //设置订阅位置服务状态变化的相关参数.
        const requestInfo: geoLocationManager.LocationRequest = { 'priority': 0x203, 'scenario': 0x300,
            'maxAccuracy': 0 };
        try {
            //订阅位置服务状态变化。
            geoLocationManager.on("locationChange", requestInfo, locationChange);
        }
        catch (err) {
            hilog.info(0x0000, 'testTag', '%{public}s', err);
        }
    }
    /**
     * 返回地理描述位置
     * @param location 地理坐标位置
     */
    static async getAddr(location: geoLocationManager.Location) {
        console.log(TAG + '正在获取城市位置信息----');
        let geoAddress = new Array<geoLocationManager.GeoAddress>();
        await geoLocationManager.getAddressesFromLocation(location).then((data) => {
            geoAddress = data;
        });
        console.log(TAG + '城市位置信息为----' + JSON.stringify(geoAddress));
        return geoAddress;
    }
    /**
     * 根据地理位置对象，返回字符串形式的经纬度
     * @param location  地理位置
     */
    static getLatLon(location: geoLocationManager.Location) {
        let latitude = location.latitude;
        let longitude = location.longitude;
        let addressLL = Number(latitude).toFixed(6) + ','
            + Number(longitude).toFixed(6);
        return addressLL;
    }
}
