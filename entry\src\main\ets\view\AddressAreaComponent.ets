/**
 * 当前位置视图组件。由位置图标和位置信息组成
 */
@Component
export struct AddressAreaComponent {
  @Link city: string
  build() {
    Column() {
      Row() {
        Image($r('app.media.location'))
          .height($r('app.float.loction_pic_size'))
          .width($r('app.float.loction_pic_size'))
        Text(this.city)
          .fontSize($r('app.float.title_font_size'))
      }
    }.margin({
      top: $r('app.float.common_space_size')
    })
  }
}