if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ListHourlyItemComponent_Params {
    itemInfo?: HourlyViewModel;
}
import type { HourlyViewModel } from '../viewmodel/HourlyViewModel';
export class ListHourlyItemComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__itemInfo = new SynchedPropertyObjectOneWayPU(params.itemInfo, this, "itemInfo");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ListHourlyItemComponent_Params) {
    }
    updateStateVars(params: ListHourlyItemComponent_Params) {
        this.__itemInfo.reset(params.itemInfo);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__itemInfo.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__itemInfo.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __itemInfo: SynchedPropertySimpleOneWayPU<HourlyViewModel>;
    get itemInfo() {
        return this.__itemInfo.get();
    }
    set itemInfo(newValue: HourlyViewModel) {
        this.__itemInfo.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/ListHourlyItemComponent.ets(9:5)", "entry");
            Column.margin({
                right: { "id": 16777232, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" },
                left: { "id": 16777232, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.itemInfo.time);
            Text.debugLine("entry/src/main/ets/view/ListHourlyItemComponent.ets(10:7)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": -1, "type": 30000, params: [this.itemInfo.img], "bundleName": "com.example.weather", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/view/ListHourlyItemComponent.ets(12:7)", "entry");
            Image.objectFit(ImageFit.Contain);
            Image.width({ "id": 16777233, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
            Image.height({ "id": 16777233, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.itemInfo.temp);
            Text.debugLine("entry/src/main/ets/view/ListHourlyItemComponent.ets(16:7)", "entry");
            Text.fontSize({ "id": 16777231, "type": 10002, params: [], "bundleName": "com.example.weather", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
