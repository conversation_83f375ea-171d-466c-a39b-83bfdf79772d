import { http } from '@kit.NetworkKit';
import hilog from '@ohos.hilog';
import { CommonConstants } from '../constants/CommonConstants';
import { WeatherModel } from '../bean/WeatherModel'
const  TAG='HttpGet '
/**
 * 网络请求工具类
 */
export class HttpGet {
  /**
   * 以get方法进行网络请求。返回天气数据实例。
   * @param addressLL 字符串格式的经纬度
   */
  async doGet(addressLL: string): Promise<WeatherModel> {
    let model: WeatherModel = new WeatherModel()
    //创建HttpRequest对象
    let httpRequest = http.createHttp();

    const url =
      `https://api.jisuapi.com/weather/query?appkey=${CommonConstants.APP_CODE}&city=&location=${addressLL}`;

    try {
      const response = await httpRequest.request(url, {
        method: http.RequestMethod.GET,
      });

      // 检查响应状态码
      if (response.responseCode === 200) {
        const result :WeatherModel= JSON.parse(response.result.toString());

        if (result.status === 0 && result.msg === "ok") {
          // 将返回的 result 赋值给 model
          model = result as WeatherModel;
          console.log(TAG+"天气数据加载成功:", JSON.stringify(model));
        } else {
          console.error(TAG+"API 返回错误:", result.msg);
        }
      } else {
        console.error(TAG+"HTTP 请求失败，状态码:", response.responseCode);
      }
    } catch (error) {
      console.error(TAG+"请求天气数据失败:", JSON.stringify(error));
    } finally {
      httpRequest.destroy(); // 释放 HTTP 资源
    }

    return model
  }
}