if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LocationDiagnostic_Params {
    diagnosticResult?: string;
    isRunning?: boolean;
}
import geoLocationManager from "@ohos:geoLocationManager";
import { GeoDataHelper } from "@normalized:N&&&entry/src/main/ets/dal/GeoDataHelper&";
import abilityAccessCtrl from "@ohos:abilityAccessCtrl";
import type { Permissions as Permissions } from "@ohos:abilityAccessCtrl";
class LocationDiagnostic extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__diagnosticResult = new ObservedPropertySimplePU('点击开始诊断按钮进行位置服务检查', this, "diagnosticResult");
        this.__isRunning = new ObservedPropertySimplePU(false, this, "isRunning");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LocationDiagnostic_Params) {
        if (params.diagnosticResult !== undefined) {
            this.diagnosticResult = params.diagnosticResult;
        }
        if (params.isRunning !== undefined) {
            this.isRunning = params.isRunning;
        }
    }
    updateStateVars(params: LocationDiagnostic_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__diagnosticResult.purgeDependencyOnElmtId(rmElmtId);
        this.__isRunning.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__diagnosticResult.aboutToBeDeleted();
        this.__isRunning.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __diagnosticResult: ObservedPropertySimplePU<string>;
    get diagnosticResult() {
        return this.__diagnosticResult.get();
    }
    set diagnosticResult(newValue: string) {
        this.__diagnosticResult.set(newValue);
    }
    private __isRunning: ObservedPropertySimplePU<boolean>;
    get isRunning() {
        return this.__isRunning.get();
    }
    set isRunning(newValue: boolean) {
        this.__isRunning.set(newValue);
    }
    async runDiagnostic() {
        this.isRunning = true;
        this.diagnosticResult = '正在进行位置服务诊断...\n\n';
        let result = '';
        // 1. 检查位置服务是否启用
        try {
            const isEnabled = geoLocationManager.isLocationEnabled();
            result += `✓ 位置服务状态: ${isEnabled ? '已启用' : '未启用'}\n`;
            if (!isEnabled) {
                result += '❌ 请在设备设置中开启位置服务\n';
            }
        }
        catch (error) {
            result += `❌ 检查位置服务状态失败: ${JSON.stringify(error)}\n`;
        }
        // 2. 检查位置权限
        try {
            let atManager = abilityAccessCtrl.createAtManager();
            let bundleInfo = await import("@ohos:AbilityKit").then(kit => kit.bundleManager.getBundleInfoForSelf(kit.bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION));
            let tokenId = bundleInfo.appInfo.accessTokenId;
            const permissions: Permissions[] = [
                'ohos.permission.LOCATION',
                'ohos.permission.APPROXIMATELY_LOCATION'
            ];
            for (let permission of permissions) {
                let grantStatus = await atManager.checkAccessToken(tokenId, permission);
                result += `✓ ${permission}: ${grantStatus === 0 ? '已授权' : '未授权'}\n`;
            }
        }
        catch (error) {
            result += `❌ 检查权限失败: ${JSON.stringify(error)}\n`;
        }
        // 3. 尝试获取最后位置
        try {
            result += '\n--- 尝试获取最后位置 ---\n';
            const lastLocation = await geoLocationManager.getLastLocation();
            if (lastLocation) {
                result += `✓ 最后位置获取成功:\n`;
                result += `  纬度: ${lastLocation.latitude}\n`;
                result += `  经度: ${lastLocation.longitude}\n`;
                result += `  精度: ${lastLocation.accuracy}米\n`;
                result += `  时间: ${new Date(lastLocation.timeStamp).toLocaleString()}\n`;
            }
            else {
                result += `⚠️ 没有最后位置信息\n`;
            }
        }
        catch (error) {
            result += `❌ 获取最后位置失败: ${JSON.stringify(error)}\n`;
        }
        // 4. 尝试获取当前位置
        try {
            result += '\n--- 尝试获取当前位置 ---\n';
            const currentLocation = await GeoDataHelper.getCurrentLocation();
            if (currentLocation) {
                result += `✓ 当前位置获取成功:\n`;
                result += `  纬度: ${currentLocation.latitude}\n`;
                result += `  经度: ${currentLocation.longitude}\n`;
                result += `  精度: ${currentLocation.accuracy}米\n`;
                // 5. 尝试地址解析
                try {
                    result += '\n--- 尝试地址解析 ---\n';
                    const addresses = await GeoDataHelper.getAddr(currentLocation);
                    if (addresses && addresses.length > 0) {
                        result += `✓ 地址解析成功:\n`;
                        result += `  国家: ${addresses[0].countryName || '未知'}\n`;
                        result += `  省份: ${addresses[0].administrativeArea || '未知'}\n`;
                        result += `  城市: ${addresses[0].locality || '未知'}\n`;
                        result += `  区域: ${addresses[0].subLocality || '未知'}\n`;
                    }
                    else {
                        result += `⚠️ 地址解析返回空结果\n`;
                    }
                }
                catch (error) {
                    result += `❌ 地址解析失败: ${JSON.stringify(error)}\n`;
                }
            }
            else {
                result += `❌ 当前位置获取失败\n`;
            }
        }
        catch (error) {
            result += `❌ 获取当前位置异常: ${JSON.stringify(error)}\n`;
        }
        // 6. 诊断建议
        result += '\n--- 诊断建议 ---\n';
        if (result.includes('未启用')) {
            result += '• 请在设备设置 > 隐私 > 位置服务中开启位置服务\n';
        }
        if (result.includes('未授权')) {
            result += '• 请在应用权限设置中授予位置权限\n';
        }
        if (result.includes('获取失败') || result.includes('获取异常')) {
            result += '• 请确保在室外或窗边，GPS信号良好的地方\n';
            result += '• 可以尝试重启应用或设备\n';
        }
        this.diagnosticResult = result;
        this.isRunning = false;
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 20 });
            Column.debugLine("entry/src/main/ets/pages/Test/LocationDiagnostic.ets(121:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('位置服务诊断');
            Text.debugLine("entry/src/main/ets/pages/Test/LocationDiagnostic.ets(122:7)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ top: 50 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('开始诊断');
            Button.debugLine("entry/src/main/ets/pages/Test/LocationDiagnostic.ets(127:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.fontSize(18);
            Button.backgroundColor('#007DFF');
            Button.enabled(!this.isRunning);
            Button.onClick(() => {
                this.runDiagnostic();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isRunning) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/Test/LocationDiagnostic.ets(138:9)", "entry");
                        LoadingProgress.width(50);
                        LoadingProgress.height(50);
                    }, LoadingProgress);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/Test/LocationDiagnostic.ets(143:7)", "entry");
            Scroll.width('100%');
            Scroll.height('60%');
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Auto);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.diagnosticResult);
            Text.debugLine("entry/src/main/ets/pages/Test/LocationDiagnostic.ets(144:9)", "entry");
            Text.fontSize(14);
            Text.margin({ left: 20, right: 20 });
            Text.textAlign(TextAlign.Start);
            Text.width('90%');
        }, Text);
        Text.pop();
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('此页面帮助诊断位置服务相关问题');
            Text.debugLine("entry/src/main/ets/pages/Test/LocationDiagnostic.ets(155:7)", "entry");
            Text.fontSize(12);
            Text.fontColor(Color.Gray);
            Text.margin({ left: 20, right: 20 });
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "LocationDiagnostic";
    }
}
registerNamedRoute(() => new LocationDiagnostic(undefined, {}), "", { bundleName: "com.example.weather", moduleName: "entry", pagePath: "pages/Test/LocationDiagnostic", pageFullPath: "entry/src/main/ets/pages/Test/LocationDiagnostic", integratedHsp: "false", moduleType: "followWithHap" });
