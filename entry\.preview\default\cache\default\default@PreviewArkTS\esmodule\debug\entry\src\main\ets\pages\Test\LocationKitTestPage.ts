if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LocationKitTestPage_Params {
    message?: string;
    location?: geoLocationManager.Location | null;
}
import { checkPermissions } from "@normalized:N&&&entry/src/main/ets/pages/Test/PermissionGrant&";
import geoLocationManager from "@ohos:geoLocationManager";
import promptAction from "@ohos:promptAction";
const TAG = 'LoacationKitTestPage ';
class LocationKitTestPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__message = new ObservedPropertySimplePU('hello', this, "message");
        this.__location = new ObservedPropertyObjectPU(null, this, "location");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LocationKitTestPage_Params) {
        if (params.message !== undefined) {
            this.message = params.message;
        }
        if (params.location !== undefined) {
            this.location = params.location;
        }
    }
    updateStateVars(params: LocationKitTestPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__message.purgeDependencyOnElmtId(rmElmtId);
        this.__location.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__message.aboutToBeDeleted();
        this.__location.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __message: ObservedPropertySimplePU<string>;
    get message() {
        return this.__message.get();
    }
    set message(newValue: string) {
        this.__message.set(newValue);
    }
    private __location: ObservedPropertyObjectPU<geoLocationManager.Location | null>;
    get location() {
        return this.__location.get();
    }
    set location(newValue: geoLocationManager.Location | null) {
        this.__location.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 20 });
            Column.debugLine("entry/src/main/ets/pages/Test/LocationKitTestPage.ets(12:5)", "entry");
            Column.height('100%');
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('用户申请位置授权');
            Button.debugLine("entry/src/main/ets/pages/Test/LocationKitTestPage.ets(13:7)", "entry");
            Button.onClick(async () => {
                const promiseString: Promise<string> = checkPermissions();
                checkPermissions();
                promiseString.then((stringValue: string) => {
                    this.message = stringValue;
                });
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('判断位置开关是否打开');
            Button.debugLine("entry/src/main/ets/pages/Test/LocationKitTestPage.ets(22:7)", "entry");
            Button.onClick(() => {
                try {
                    let locationEnabled = geoLocationManager.isLocationEnabled();
                    if (locationEnabled == true)
                        promptAction.showToast({ message: '开关已打开' });
                    else
                        promptAction.showToast({ message: '开关未打开' });
                }
                catch (err) {
                    promptAction.showToast({ message: "errCode:" + err.code + ", message:" + err.message });
                }
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('获取当前的实时位置');
            Button.debugLine("entry/src/main/ets/pages/Test/LocationKitTestPage.ets(33:7)", "entry");
            Button.onClick(() => {
                try {
                    geoLocationManager.on('locationChange', {}, (location) => {
                        this.location = location;
                    });
                }
                catch (err) {
                    promptAction.showToast({ message: "errCode:" + err.code + ", message:" + err.message });
                }
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('判断服务是否可用');
            Button.debugLine("entry/src/main/ets/pages/Test/LocationKitTestPage.ets(44:7)", "entry");
            Button.onClick(() => {
                const isAvailable = geoLocationManager.isGeocoderAvailable();
                promptAction.showToast({ message: '服务是否支持' + isAvailable });
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('地理解析');
            Button.debugLine("entry/src/main/ets/pages/Test/LocationKitTestPage.ets(50:7)", "entry");
            Button.onClick(async () => {
                const location = await geoLocationManager.getAddressesFromLocationName({
                    description: '广州市'
                });
                promptAction.showToast({ message: JSON.stringify(location, null, 2) });
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(JSON.stringify(ObservedObject.GetRawObject(this.location), null, 2));
            Text.debugLine("entry/src/main/ets/pages/Test/LocationKitTestPage.ets(58:7)", "entry");
        }, Text);
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "LocationKitTestPage";
    }
}
registerNamedRoute(() => new LocationKitTestPage(undefined, {}), "", { bundleName: "com.example.weather", moduleName: "entry", pagePath: "pages/Test/LocationKitTestPage", pageFullPath: "entry/src/main/ets/pages/Test/LocationKitTestPage", integratedHsp: "false", moduleType: "followWithHap" });
