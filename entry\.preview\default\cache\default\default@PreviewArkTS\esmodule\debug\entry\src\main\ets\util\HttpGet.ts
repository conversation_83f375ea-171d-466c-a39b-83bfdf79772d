import http from "@ohos:net.http";
import { CommonConstants } from "@normalized:N&&&entry/src/main/ets/constants/CommonConstants&";
import { WeatherModel } from "@normalized:N&&&entry/src/main/ets/bean/WeatherModel&";
import { MockData } from "@normalized:N&&&entry/src/main/ets/util/MockData&";
const TAG = 'HttpGet ';
/**
 * 网络请求工具类
 */
export class HttpGet {
    /**
     * 以get方法进行网络请求。返回天气数据实例。
     * @param addressLL 字符串格式的经纬度
     */
    async doGet(addressLL: string): Promise<WeatherModel> {
        // 检查是否使用模拟数据
        if (MockData.shouldUseMockData()) {
            console.log(TAG + "使用模拟数据");
            return MockData.getMockWeatherData();
        }
        let model: WeatherModel = new WeatherModel();
        //创建HttpRequest对象
        let httpRequest = http.createHttp();
        const url = `https://api.jisuapi.com/weather/query?appkey=${CommonConstants.APP_CODE}&city=&location=${addressLL}`;
        console.log(TAG + "请求URL:", url);
        console.log(TAG + "请求经纬度:", addressLL);
        try {
            const response = await httpRequest.request(url, {
                method: http.RequestMethod.GET,
                connectTimeout: 10000,
                readTimeout: 10000, // 10秒读取超时
            });
            console.log(TAG + "HTTP响应状态码:", response.responseCode);
            console.log(TAG + "HTTP响应头:", JSON.stringify(response.header));
            // 检查响应状态码
            if (response.responseCode === 200) {
                console.log(TAG + "原始响应数据:", response.result.toString());
                try {
                    const result: WeatherModel = JSON.parse(response.result.toString());
                    console.log(TAG + "解析后的数据:", JSON.stringify(result));
                    if (result.status === 0 && result.msg === "ok") {
                        // 将返回的 result 赋值给 model
                        model = result as WeatherModel;
                        console.log(TAG + "天气数据加载成功");
                        console.log(TAG + "当前温度:", model.result.temp);
                        console.log(TAG + "天气状况:", model.result.weather);
                        return model;
                    }
                    else {
                        console.error(TAG + "API 返回错误 - 状态码:", result.status, "错误信息:", result.msg);
                        // API返回错误时使用模拟数据
                        console.log(TAG + "API错误，使用模拟数据作为备用方案");
                        return MockData.getMockWeatherData();
                    }
                }
                catch (parseError) {
                    console.error(TAG + "JSON解析失败:", JSON.stringify(parseError));
                    console.error(TAG + "原始数据:", response.result.toString());
                    // 解析失败时使用模拟数据
                    console.log(TAG + "数据解析失败，使用模拟数据作为备用方案");
                    return MockData.getMockWeatherData();
                }
            }
            else {
                console.error(TAG + "HTTP 请求失败，状态码:", response.responseCode);
                console.error(TAG + "响应内容:", response.result?.toString());
                // HTTP请求失败时使用模拟数据
                console.log(TAG + "HTTP请求失败，使用模拟数据作为备用方案");
                return MockData.getMockWeatherData();
            }
        }
        catch (error) {
            console.error(TAG + "网络请求异常:", JSON.stringify(error));
            if (error.code) {
                console.error(TAG + "错误代码:", error.code);
            }
            if (error.message) {
                console.error(TAG + "错误信息:", error.message);
            }
            // 网络异常时使用模拟数据
            console.log(TAG + "网络异常，使用模拟数据作为备用方案");
            return MockData.getMockWeatherData();
        }
        finally {
            httpRequest.destroy(); // 释放 HTTP 资源
        }
    }
}
