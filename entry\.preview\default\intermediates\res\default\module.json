{"app": {"bundleName": "com.example.weather", "vendor": "example", "versionCode": 1000000, "versionName": "1.0.0", "icon": "$media:layered_image", "label": "$string:app_name", "apiReleaseType": "Release", "compileSdkVersion": "*********", "targetAPIVersion": 50005017, "minAPIVersion": 50002014, "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug", "debug": true, "iconId": 16777218, "labelId": 16777219}, "module": {"name": "entry", "type": "entry", "description": "$string:ability_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:ability_desc", "icon": "$media:layered_image", "label": "$string:ability_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}], "descriptionId": 16777222, "iconId": 16777218, "labelId": 16777223, "startWindowIconId": 16777229, "startWindowBackgroundId": 16777220, "ohmurl": "@normalized:N&&&entry/src/main/ets/entryability/EntryAbility&"}], "extensionAbilities": [{"name": "EntryBackupAbility", "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets", "type": "backup", "exported": false, "metadata": [{"name": "ohos.extension.backup", "resource": "$profile:backup_config", "resourceId": 16777239}], "ohmurl": "@normalized:N&&&entry/src/main/ets/entrybackupability/EntryBackupAbility&"}], "requestPermissions": [{"name": "ohos.permission.INTERNET"}, {"name": "ohos.permission.LOCATION", "usedScene": {}, "reason": "$string:premission_location_reason", "reasonId": 16777224}, {"name": "ohos.permission.LOCATION_IN_BACKGROUND", "usedScene": {}, "reason": "$string:premission_location_reason", "reasonId": 16777224}, {"name": "ohos.permission.APPROXIMATELY_LOCATION", "usedScene": {}, "reason": "$string:premission_location_reason", "reasonId": 16777224}], "packageName": "entry", "virtualMachine": "ark12.0.6.0", "compileMode": "esmodule", "dependencies": [], "descriptionId": 16777222}}