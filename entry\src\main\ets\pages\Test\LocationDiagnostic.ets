import { geoLocationManager } from '@kit.LocationKit';
import { GeoDataHelper } from '../../dal/GeoDataHelper';
import { abilityAccessCtrl, Permissions } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';

/**
 * 位置诊断页面，用于检查位置服务的各种状态
 */
@Entry
@Component
struct LocationDiagnostic {
  @State diagnosticResult: string = '点击开始诊断按钮进行位置服务检查';
  @State isRunning: boolean = false;

  async runDiagnostic() {
    this.isRunning = true;
    this.diagnosticResult = '正在进行位置服务诊断...\n\n';

    let result = '';

    // 1. 检查位置服务是否启用
    try {
      const isEnabled = geoLocationManager.isLocationEnabled();
      result += `✓ 位置服务状态: ${isEnabled ? '已启用' : '未启用'}\n`;
      if (!isEnabled) {
        result += '❌ 请在设备设置中开启位置服务\n';
      }
    } catch (error) {
      result += `❌ 检查位置服务状态失败: ${JSON.stringify(error)}\n`;
    }

    // 2. 检查位置权限
    try {
      let atManager = abilityAccessCtrl.createAtManager();
      let bundleInfo = await import('@kit.AbilityKit').then(kit => 
        kit.bundleManager.getBundleInfoForSelf(kit.bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION)
      );
      let tokenId = bundleInfo.appInfo.accessTokenId;

      const permissions: Permissions[] = [
        'ohos.permission.LOCATION',
        'ohos.permission.APPROXIMATELY_LOCATION'
      ];

      for (let permission of permissions) {
        let grantStatus = await atManager.checkAccessToken(tokenId, permission);
        result += `✓ ${permission}: ${grantStatus === 0 ? '已授权' : '未授权'}\n`;
      }
    } catch (error) {
      result += `❌ 检查权限失败: ${JSON.stringify(error)}\n`;
    }

    // 3. 尝试获取最后位置
    try {
      result += '\n--- 尝试获取最后位置 ---\n';
      const lastLocation = await geoLocationManager.getLastLocation();
      if (lastLocation) {
        result += `✓ 最后位置获取成功:\n`;
        result += `  纬度: ${lastLocation.latitude}\n`;
        result += `  经度: ${lastLocation.longitude}\n`;
        result += `  精度: ${lastLocation.accuracy}米\n`;
        result += `  时间: ${new Date(lastLocation.timeStamp).toLocaleString()}\n`;
      } else {
        result += `⚠️ 没有最后位置信息\n`;
      }
    } catch (error) {
      result += `❌ 获取最后位置失败: ${JSON.stringify(error)}\n`;
    }

    // 4. 尝试获取当前位置
    try {
      result += '\n--- 尝试获取当前位置 ---\n';
      const currentLocation = await GeoDataHelper.getCurrentLocation();
      if (currentLocation) {
        result += `✓ 当前位置获取成功:\n`;
        result += `  纬度: ${currentLocation.latitude}\n`;
        result += `  经度: ${currentLocation.longitude}\n`;
        result += `  精度: ${currentLocation.accuracy}米\n`;
        
        // 5. 尝试地址解析
        try {
          result += '\n--- 尝试地址解析 ---\n';
          const addresses = await GeoDataHelper.getAddr(currentLocation);
          if (addresses && addresses.length > 0) {
            result += `✓ 地址解析成功:\n`;
            result += `  国家: ${addresses[0].countryName || '未知'}\n`;
            result += `  省份: ${addresses[0].administrativeArea || '未知'}\n`;
            result += `  城市: ${addresses[0].locality || '未知'}\n`;
            result += `  区域: ${addresses[0].subLocality || '未知'}\n`;
          } else {
            result += `⚠️ 地址解析返回空结果\n`;
          }
        } catch (error) {
          result += `❌ 地址解析失败: ${JSON.stringify(error)}\n`;
        }
      } else {
        result += `❌ 当前位置获取失败\n`;
      }
    } catch (error) {
      result += `❌ 获取当前位置异常: ${JSON.stringify(error)}\n`;
    }

    // 6. 诊断建议
    result += '\n--- 诊断建议 ---\n';
    if (result.includes('未启用')) {
      result += '• 请在设备设置 > 隐私 > 位置服务中开启位置服务\n';
    }
    if (result.includes('未授权')) {
      result += '• 请在应用权限设置中授予位置权限\n';
    }
    if (result.includes('获取失败') || result.includes('获取异常')) {
      result += '• 请确保在室外或窗边，GPS信号良好的地方\n';
      result += '• 可以尝试重启应用或设备\n';
    }

    this.diagnosticResult = result;
    this.isRunning = false;
  }

  build() {
    Column({ space: 20 }) {
      Text('位置服务诊断')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 })

      Button('开始诊断')
        .width('80%')
        .height(50)
        .fontSize(18)
        .backgroundColor('#007DFF')
        .enabled(!this.isRunning)
        .onClick(() => {
          this.runDiagnostic();
        })

      if (this.isRunning) {
        LoadingProgress()
          .width(50)
          .height(50)
      }

      Scroll() {
        Text(this.diagnosticResult)
          .fontSize(14)
          .margin({ left: 20, right: 20 })
          .textAlign(TextAlign.Start)
          .width('90%')
      }
      .width('100%')
      .height('60%')
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Auto)

      Text('此页面帮助诊断位置服务相关问题')
        .fontSize(12)
        .fontColor(Color.Gray)
        .margin({ left: 20, right: 20 })
        .textAlign(TextAlign.Center)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
