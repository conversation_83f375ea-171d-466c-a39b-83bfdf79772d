{
  "module": {
    "name": "entry",
    "type": "entry",
    "description": "$string:ability_desc",
    "mainElement": "EntryAbility",
    "deviceTypes": [
      "phone",
      "tablet",
      "2in1"
    ],
    "deliveryWithInstall": true,
    "installationFree": false,
    "pages": "$profile:main_pages",
    "abilities": [
      {
        "name": "EntryAbility",
        "srcEntry": "./ets/entryability/EntryAbility.ets",
        "description": "$string:ability_desc",
        "icon": "$media:layered_image",
        "label": "$string:ability_label",
        "startWindowIcon": "$media:startIcon",
        "startWindowBackground": "$color:start_window_background",
        "exported": true,
        "skills": [
          {
            "entities": [
              "entity.system.home"
            ],
            "actions": [
              "action.system.home"
            ]
          }
        ]
      }
    ],
    "extensionAbilities": [
      {
        "name": "EntryBackupAbility",
        "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets",
        "type": "backup",
        "exported": false,
        "metadata": [
          {
            "name": "ohos.extension.backup",
            "resource": "$profile:backup_config"
          }
        ],
      }
    ],
    "requestPermissions": [
      {
        "name": "ohos.permission.INTERNET"
      },
      {
        "name": "ohos.permission.LOCATION",
        "usedScene": {},
        "reason": "$string:premission_location_reason"
      },
      {
        "name": "ohos.permission.LOCATION_IN_BACKGROUND",
        "usedScene": {},
        "reason": "$string:premission_location_reason"
      },
      {
        "name": "ohos.permission.APPROXIMATELY_LOCATION",
        "usedScene": {},
        "reason": "$string:premission_location_reason"
      }
    ]
  }
}