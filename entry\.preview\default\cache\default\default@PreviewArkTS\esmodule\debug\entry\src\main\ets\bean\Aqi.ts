import { Aqiinfo } from "@normalized:N&&&entry/src/main/ets/bean/Aqiinfo&";
/**
 * 每个字段的解释参考 https://www.jisuapi.com/api/weather
 */
export class Aqi {
    public so2: string = '';
    public so224: string = '';
    public no2: string = '';
    public no224: string = '';
    public co: string = '';
    public co24: string = '';
    public o3: string = '';
    public o38: string = '';
    public o324: string = '';
    public pm10: string = '';
    public pm1024: string = '';
    public pm2_5: string = '';
    public pm2_524: string = '';
    public iso2: string = '';
    public ino2: string = '';
    public ico: string = '';
    public io3: string = '';
    public io38: string = '';
    public ipm10: string = '';
    public ipm2_5: string = '';
    public aqi: string = '';
    public primarypollutant: string = '';
    public quality: string = '';
    public timepoint: Date = new Date();
    public aqiinfo: Aqiinfo = new Aqiinfo();
}
