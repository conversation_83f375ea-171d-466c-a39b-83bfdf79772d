import { AqiViewModel } from '../viewmodel/AqiViewModel';
/**
 * AQI视图组件。由AQI提示消息、AQI值的图形展示及空气质量文字描述组成。
 */
@Component
export struct AqiAreaComponent {
  @Link aqiViewModel: AqiViewModel

  build() {
    Column() {
      Row() {
        Column() {
          Text("A Q I ：").fontSize($r('app.float.common_font_size'))
        }
        .layoutWeight(3)
        Column() {
          Stack({ alignContent: Alignment.Center }) {
            Gauge({ value: this.aqiViewModel.aqi, min: 0, max: 500 })
              .startAngle(210)
              .endAngle(150)
                //AQI的不同值对应不同的颜色柱
              .colors([[0x00FF00, 0.1], [0xFFFF00, 0.1],
                [0xFF6100, 0.1], [0xFF0000, 0.1], [0xA020F0, 0.2],
                [0x8B0000, 0.4]])
              .strokeWidth(15)
            Text(this.aqiViewModel.aqi.toString())
              .fontSize($r('app.float.big_font_size'))
          }
        }.layoutWeight(4)
        Column() {
          Text(this.aqiViewModel.quality)
            .fontSize($r('app.float.common_font_size'))
        }.layoutWeight(3)
      }
    }
  }
}